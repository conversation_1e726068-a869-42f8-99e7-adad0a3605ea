//happy
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/model/comment/comment_access_detail.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/post_tagged_objects_response/post_tagged_objects_response.dart';
import 'package:swadesic/model/product_comment_response/add_child_comment_response.dart';
import 'package:swadesic/model/product_comment_response/add_parent_comment_response.dart';
import 'package:swadesic/model/product_comment_response/add_product_rating_response.dart';
import 'package:swadesic/model/product_comment_response/product_all_comment_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/api_frequency_checker/api_frequency_checker.dart';
import 'package:swadesic/services/cache_storage/cache_storage_service.dart';
import 'package:swadesic/services/cache_storage/storage_keys.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';

class PostService {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  PostService() {
    httpService = HttpService();
  }

  // endregion

  // region Get single post
  Future<PostDetail> getSinglePost({required String postReference}) async {
    // endregion
    Map<String, dynamic> response;
    // Query
    var query = '''
 query Posts {
    posts(postReferences: "$postReference") {


     commentCount
        repostCount
        repostPlusCount
        saveCount
        shareCount
         postReference
        postText
        createdDate
        isDeleted
        likeCount
        analyticsViewCount
        taggedUsersCount
        taggedStoresCount
        taggedProductsCount
        postImages {
            mediaId
            mediaType
            mediaPath
            order
        }
        likeStatus(visitorReference:"${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
        repostStatus(visitorReference:"${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
        saveStatus(visitorReference:"${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
        createdBy {
        subscriptionType
            reference
            handle
            icon
            entityType
          name
          phoneNumber
          pincode
          city
          isDeleted
          createdDate
          categoryName
          followersOrSupportersCount
          followingOrSupportingCount
          followStatus(visitorReference: "U1710097181792")
        }

        contentCategory(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
        contentHeaders(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
         {
          handle
            reference
            }
      contentHeaderText(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")


    }
}
  ''';

    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);

    // Parse the response and extract the list of PostDetail instances
    late PostDetail post;

    if (response.containsKey('data') &&
        response['data'] != null &&
        response['data']['posts'] != null) {
      Map<String, dynamic> postJson =
          response['data']['posts'][0]; // Assuming it's a single post
      post = PostDetail.fromJson(postJson);
    }
    //print(response);
    return post;
  }

  // endregion

  // region Get store or user posts
  Future<List<PostDetail>> getStoreOrUserPosts(
      {required String creatorReference,
      required int limit,
      required int offset}) async {
    // endregion
    //Get app data from cache
    await AppDataService().getAppData();

    // Get visitor reference for API call
    String visitorReference = AppConstants.appData.isUserView!
        ? AppConstants.appData.userReference!
        : AppConstants.appData.storeReference!;

    // Construct the URL for the lean user or store posts API
    String url =
        "${AppConstants.baseUrl}/lean/get_user_or_store_posts/?limit=$limit&offset=$offset&visitor_reference=$visitorReference&entity_reference=$creatorReference";

    debugPrint("Calling lean user or store posts API: $url");

    // Execute the API call
    Map<String, dynamic> response = await httpService.getApiCall(url);

    // Parse the response and extract the list of posts
    List<PostDetail> postDetails = [];

    if (response.containsKey('message') &&
        response['message'] == 'success' &&
        response['data'] != null) {
      List<dynamic> dataJson = response['data'];

      // Process each post in the response using fromLeanJson
      postDetails =
          dataJson.map((json) => PostDetail.fromLeanJson(json)).toList();
    }

    debugPrint(
        "Lean user or store posts API returned ${postDetails.length} posts");
    return postDetails;
  }

  // endregion

  // region Get store or user re-posts
  Future<List<dynamic>> getStoreOrUserRePosts(
      {required String creatorReference,
      required int limit,
      required int offset,
      required BuildContext context}) async {
    //Get reference to logged in user data model
    var loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);

    // endregion
    Map<String, dynamic> response;
    // Query
    var query = '''
query GetReposts {
    getReposts(reference: "$creatorReference", limit: $limit, offset: $offset) {
        ... on Neo4jPostType {
            commentCount
            repostCount
            repostPlusCount
            saveCount
            shareCount
            postReference
            postText
            createdDate
            isDeleted
            likeCount
            analyticsViewCount
            postImages {
                mediaId
                mediaType
                mediaPath
                order
            }
            likeStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            repostStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            saveStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            createdBy {
                reference
                handle
                icon
                entityType
                name
                phoneNumber
                pincode
                city
                isDeleted
                createdDate
                categoryName
                followersOrSupportersCount
                followingOrSupportingCount
                followStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
                entityTown
            }
            contentType
            contentCategory(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            contentHeaders(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}") {
                handle
                reference
            }
            contentHeaderText(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")


    }
        ... on Neo4jProductType {
            contentType
            contentCategory(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            likeCount
            commentCount
            repostCount
            repostPlusCount
            storeId
            saveCount
            shareCount
            createdDate
            isBuyEnabled(userPincode: "${AppConstants.appData.pinCode}")
            productStatusMessage(userPincode: "${AppConstants.appData.pinCode}")
            likeStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            repostStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            saveStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            productReference
            productName
            brandName
            productCategory
            storeReference
            active
            productDescription
            promotionLink
            hashtags
            inStock
            mrpPrice
            sellingPrice
            isDeleted
            swadeshiMade
            swadeshiBrand
            swadeshiOwned
            targetedGender
            ordersCount
            returnsCount
            cancelsCount
            analyticsViewCount
            contentHeaders(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}") {
                handle
                reference
            }
            productImages {
                mediaId
                mediaType
                mediaPath
                order
            }
            productid
            createdBy {
                reference
                entityType
                handle
                name
                icon
                phoneNumber
                pincode
                city
                isDeleted
                createdDate
                categoryName
                followersOrSupportersCount
                followingOrSupportingCount
                followStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
                entityTown
            }
            modifiedDate
            configReceiveOrders
            rating
            countOfRatings
            productVersion
            location
            returnPeriod
            returnCostOn
            returnConditions
            returnPickUp
            deliveryBy
            deliveryPartner
            deliveryFee
            deliverySettingsType
            refundResponsibility {
                itemHeading
                itemText
                itemSubtext
            }
            logisticPartnerName
            deliverability(userPincode: "${AppConstants.appData.pinCode}")
            fulfillmentOptions
            disclaimerMessage
        }
    }
}
''';

    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);

    List<dynamic> postDetails = [];

    if (response.containsKey('data') &&
        response['data'] != null &&
        response['data']['getReposts'] != null) {
      List<dynamic> feedJson = response['data']['getReposts'];

      postDetails = feedJson
          .map((json) {
            if (json != null && json.isNotEmpty) {
              if (json['contentType'] == EntityType.POST.name) {
                return PostDetail.fromJson(json);
              } else if (json['contentType'] == EntityType.PRODUCT.name) {
                return Product.fromJson(json);
              }
              throw Exception('Unknown type: ${json['type']}');
            }
            return null;
          })
          .where((element) => element != null)
          .toList();
    }

    //print(response);
    return postDetails;
  }

  // endregion

  //region Get feed
  Future<List<dynamic>> getFeeds(
      {required int limit,
      required int offset,
      required BuildContext context}) async {
    //Get reference to logged in user data model
    var loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);

    //Get app data from cache
    await AppDataService().getAppData();

    //If static user then return empty post
    // if(AppConstants.appData.userReference == AppConstants.staticUser){
    //   List<PostDetail> emptyPosts = [];
    //   return emptyPosts;
    // }

    // endregion
    Map<String, dynamic> response;
    //print("Feed Api AppConstants: ${json.encode(AppConstants.appData.toJson())}");

    var query = '''
query Feed {
    feed(reference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}", limit: $limit, offset: $offset) {
        ... on Neo4jPostType {
            commentCount
            repostCount
            repostPlusCount
            saveCount
            shareCount
            postReference
            postText
            createdDate
            isDeleted
            likeCount
            postImages {
                mediaId
                mediaType
                mediaPath
                order
            }
            likeStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            repostStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            saveStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            createdBy {
            subscriptionType
                reference
                handle
                icon
                entityType
                name
                phoneNumber
                pincode
                city
                isDeleted
                createdDate
                categoryName
                followersOrSupportersCount
                followingOrSupportingCount
                followStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
                entityTown
            }
            contentType
            contentCategory(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            contentHeaders(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}") {
                handle
                reference
            }
            contentHeaderText(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")

        }
        ... on Neo4jProductType {
                    contentHeaderText(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")

            contentType
            contentCategory(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            likeCount
            commentCount
            repostCount
            repostPlusCount
            storeId
            saveCount
            shareCount
            createdDate
            isBuyEnabled(userPincode: "${AppConstants.appData.pinCode}")
            productStatusMessage(userPincode: "${AppConstants.appData.pinCode}")

            likeStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            repostStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            saveStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            productReference
            productName
            brandName
            productCategory
            storeReference
            active
            productDescription
            promotionLink
            hashtags
            inStock
            mrpPrice
            sellingPrice
            isDeleted
            swadeshiMade
            swadeshiBrand
            swadeshiOwned
            targetedGender
            ordersCount
            returnsCount
            cancelsCount
            contentHeaders(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}") {
                handle
                reference
            }
                     contentCategory(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")

            contentHeaderText(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")

            productImages {
                mediaId
                mediaType
                mediaPath
                order
            }
            productid
            createdBy {
            subscriptionType
                reference
                entityType
                handle
                name
                icon
                phoneNumber
                pincode
                city
                isDeleted
                createdDate
                categoryName
                followersOrSupportersCount
                followingOrSupportingCount
                followStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
                entityTown
            }
            modifiedDate
            configReceiveOrders
            rating
            countOfRatings
            productVersion
            location
            returnPeriod
            returnCostOn
            returnConditions
            returnPickUp
            deliveryBy
            deliveryPartner
            deliveryFee
            deliverySettingsType
            refundResponsibility {
                itemHeading
                itemText
                itemSubtext
            }
            logisticPartnerName
            deliverability(userPincode: "${AppConstants.appData.pinCode}")
            fulfillmentOptions
            disclaimerMessage
        }
    }
}
''';

    //#region Region - Execute Request
    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);

    // return response;
    // Parse the response and extract the list of PostDetail instances
    List<dynamic> feedDetail = [];

    if (response.containsKey('data') &&
        response['data'] != null &&
        response['data']['feed'] != null) {
      List<dynamic> feedJson = response['data']['feed'];

      feedDetail = feedJson
          .map((json) {
            if (json != null && json.isNotEmpty) {
              if (json['contentType'] == EntityType.POST.name) {
                return PostDetail.fromJson(json);
              } else if (json['contentType'] == EntityType.PRODUCT.name) {
                return Product.fromJson(json);
              }
              throw Exception('Unknown type: ${json['type']}');
            }
            return null;
          })
          .where((element) => element != null)
          .toList();
    }
    //print(response);
    return feedDetail;
  }

  // endregion

  //region Get recommended posts
  Future<List<dynamic>> getRecommendedPosts(
      {required int limit,
      required int offset,
      required BuildContext context}) async {
    //Get app data from cache
    await AppDataService().getAppData();
    //Get reference to logged in user data model
    var loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);

    //If static user then return empty post
    // if(AppConstants.appData.userReference == AppConstants.staticUser){
    //   List<PostDetail> emptyPosts = [];
    //   return emptyPosts;
    // }

    // endregion
    Map<String, dynamic> response;

    var query = '''
query AllPosts {
    allPosts( limit: $limit, offset: $offset) {
        ... on Neo4jPostType {
            commentCount
            repostCount
            repostPlusCount
            saveCount
            shareCount
            postReference
            postText
            createdDate
            isDeleted
            likeCount
            postImages {
                mediaId
                mediaType
                mediaPath
                order
            }
            likeStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            repostStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            saveStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            createdBy {
            subscriptionType
                reference
                handle
                icon
                entityType
                name
                phoneNumber
                pincode
                city
                isDeleted
                createdDate
                categoryName
                followersOrSupportersCount
                followingOrSupportingCount
                followStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
                entityTown
            }
            contentType
            contentCategory(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            contentHeaders(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}") {
                handle
                reference
            }
         contentHeaderText(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")

        }
        ... on Neo4jProductType {
            contentType
            contentCategory(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            likeCount
            commentCount
            repostCount
            repostPlusCount
            storeId
            saveCount
            shareCount
            createdDate
            isBuyEnabled(userPincode: "${AppConstants.appData.pinCode}")
            productStatusMessage(userPincode: "${AppConstants.appData.pinCode}")
            likeStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            repostStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            saveStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
            productReference
            productName
            brandName
            productCategory
            storeReference
            active
            productDescription
            promotionLink
            hashtags
            inStock
            mrpPrice
            sellingPrice
            isDeleted
            swadeshiMade
            swadeshiBrand
            swadeshiOwned
            targetedGender
            ordersCount
            returnsCount
            cancelsCount
            contentHeaders(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}") {
                handle
                reference
            }
                 contentCategory(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")

         contentHeaderText(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")

            productImages {
                mediaId
                mediaType
                mediaPath
                order
            }
            productid
            createdBy {
            subscriptionType
                reference
                entityType
                handle
                name
                icon
                phoneNumber
                pincode
                city
                isDeleted
                createdDate
                categoryName
                followersOrSupportersCount
                followingOrSupportingCount
                followStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
                entityTown
            }
            modifiedDate
            configReceiveOrders
            rating
            countOfRatings
            productVersion
            location
            returnPeriod
            returnCostOn
            returnConditions
            returnPickUp
            deliveryBy
            deliveryPartner
            deliveryFee
            deliverySettingsType
            logisticPartnerName
            refundResponsibility {
                itemHeading
                itemText
                itemSubtext
            }
            deliverability(userPincode: "${AppConstants.appData.pinCode ?? 110068}")
            fulfillmentOptions
            disclaimerMessage
        }
    }
}
''';

    //#region Region - Execute Request
    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);

    // return response;
    // Parse the response and extract the list of PostDetail instances
    List<dynamic> feedDetail = [];

    if (response.containsKey('data') &&
        response['data'] != null &&
        response['data']['allPosts'] != null) {
      List<dynamic> feedJson = response['data']['allPosts'];

      feedDetail = feedJson.map((json) {
        if (json['contentType'] == EntityType.POST.name) {
          return PostDetail.fromJson(json);
        } else if (json['contentType'] == EntityType.PRODUCT.name) {
          return Product.fromJson(json);
        }
        throw Exception('Unknown type: ${json['type']}');
      }).toList();
    }
    //print(response);
    return feedDetail;
  }

  // endregion

  // region Delete post image
  Future<void> deletePostImage(
      {required String imageId, required String postReference}) async {
    // endregion
    Map<String, dynamic> response;
    // Query
    // mutation DeletePostImage {
    //   deletePostImage(mediaId: null, postReference: null) {
    //     success
    //     message
    //   }
    // }
    var query = '''
 mutation DeletePostImage {
        deletePostImage(mediaId: "$imageId",postReference:"$postReference" ) {
            success
            message
        }
    }
      ''';
    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);
  }

  // endregion

  // region Delete comment image
  Future<void> deleteCommentImage(
      {required String imageId, required String postReference}) async {
    // endregion
    Map<String, dynamic> response;
    // Query
    // mutation DeletePostImage {
    //   deletePostImage(mediaId: null, postReference: null) {
    //     success
    //     message
    //   }
    // }
    var query = '''
 mutation DeleteCommentImage {
        deleteCommentImage(commentImageId: "$imageId") {
            success
            message
        }
    }
      ''';
    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);
  }

  // endregion

  // region Delete post
  Future<bool> deletePost({required String postRefrence}) async {
    // endregion
    Map<String, dynamic> response;
    // Query
    var query = '''
 mutation DeletePost {
    deletePost(postReference: "$postRefrence") {
        success
    }
}

  ''';

    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);

    // Parse the response and extract the list of PostDetail instances
    bool postDeleteStatus = false;

    if (response.containsKey('data') &&
        response['data'] != null &&
        response['data']['deletePost'] != null) {
      postDeleteStatus = response['data']?['deletePost']?['success'];
    }
    //print(response);
    return postDeleteStatus;
  }

  // endregion

  // region Like post
  Future<bool> likePost(
      {required String postReference, required bool likeStatus}) async {
    // endregion
    Map<String, dynamic> response;

    // Query
    //   var query = '''
    //     mutation LikePost {
    //       likePost(
    //           postReference: "$postReference"
    //           storeReference: "${AppConstants.appData.isStoreView!?AppConstants.appData.storeReference:null}"
    //           userReference: "${AppConstants.appData.isUserView!?AppConstants.appData.userReference:null}"
    //       ) {
    //           isLiked
    //       }
    //   }
    // ''';

    // mutation LikeContent {
    //   likeContent(
    //       action: true
    //       contentReference: "post, product, comment"
    //       currentReference: "user/store"
    //   ) {
    //     isLiked
    //   }
    // }

    var query = '''
    mutation LikeContent {
      likeContent(
        action: $likeStatus
        contentReference: "$postReference"
        currentReference: ${AppConstants.appData.isStoreView! ? '"${AppConstants.appData.storeReference}"' : '"${AppConstants.appData.userReference}"'}
      ) {
        isLiked
      }
    }
  ''';

    //#region Region - Execute Request
    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);

    // // var body = {
    // //   "post_reference": postReference,
    // //   "liked_by_user": AppConstants.appData.isUserView! ? AppConstants.appData.userReference : "",
    // //   "liked_by_store": AppConstants.appData.isStoreView! ? AppConstants.appData.storeReference : ""
    // // };
    // //#region Region - Execute Request
    // response = await httpService.postApiCall(body, AppConstants.likeDisLikePost);
    // return response['data']['is_liked'];
    // return PostDetail.fromJson(response['data']['is_liked']);
    return response["data"]["likeContent"]["isLiked"];
  }

// endregion

  // region Repost
  Future<bool> repost(
      {required String postReference, required bool repostStatus}) async {
    // endregion
    Map<String, dynamic> response;

    // Query
    //   var query = '''
    //     mutation LikePost {
    //       likePost(
    //           postReference: "$postReference"
    //           storeReference: "${AppConstants.appData.isStoreView!?AppConstants.appData.storeReference:null}"
    //           userReference: "${AppConstants.appData.isUserView!?AppConstants.appData.userReference:null}"
    //       ) {
    //           isLiked
    //       }
    //   }
    // ''';

    // mutation LikeContent {
    //   likeContent(
    //       action: true
    //       contentReference: "post, product, comment"
    //       currentReference: "user/store"
    //   ) {
    //     isLiked
    //   }
    // }

    var query = '''
    mutation AddRepost {
      addRepost(
        action: $repostStatus
        contentReference: "$postReference"
        entityReference: ${AppConstants.appData.isStoreView! ? '"${AppConstants.appData.storeReference}"' : '"${AppConstants.appData.userReference}"'}
      ) {
        isReposted
      }
    }
  ''';

    //#region Region - Execute Request
    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);

    // // var body = {
    // //   "post_reference": postReference,
    // //   "liked_by_user": AppConstants.appData.isUserView! ? AppConstants.appData.userReference : "",
    // //   "liked_by_store": AppConstants.appData.isStoreView! ? AppConstants.appData.storeReference : ""
    // // };
    // //#region Region - Execute Request
    // response = await httpService.postApiCall(body, AppConstants.likeDisLikePost);
    // return response['data']['is_liked'];
    // return PostDetail.fromJson(response['data']['is_liked']);
    return response["data"]["addRepost"]["isReposted"];
  }

// endregion

  // region Save post
  Future<bool> savePost(
      {required String postReference, required bool saveStatus}) async {
    // endregion
    Map<String, dynamic> response;

    var query = '''
    mutation SaveContent {
      saveContent(
        action: $saveStatus
        contentReference: "$postReference"
        currentReference: ${AppConstants.appData.isStoreView! ? '"${AppConstants.appData.storeReference}"' : '"${AppConstants.appData.userReference}"'}
      ) {
        isSaved
      }
    }
  ''';

    //#region Region - Execute Request
    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);

    // // var body = {
    // //   "post_reference": postReference,
    // //   "liked_by_user": AppConstants.appData.isUserView! ? AppConstants.appData.userReference : "",
    // //   "liked_by_store": AppConstants.appData.isStoreView! ? AppConstants.appData.storeReference : ""
    // // };
    // //#region Region - Execute Request
    // response = await httpService.postApiCall(body, AppConstants.likeDisLikePost);
    // return response['data']['is_liked'];
    // return PostDetail.fromJson(response['data']['is_liked']);
    return response["data"]["saveContent"]["isSaved"];
  }

// endregion

  // region Visited post
  Future<bool> visitedPost({required String postReference}) async {
    // endregion
    Map<String, dynamic> response;

    var query = '''
   mutation AddVisit{
      addVisit(
        visitedReference: "$postReference"
        visitorReference: ${AppConstants.appData.isStoreView! ? '"${AppConstants.appData.storeReference}"' : '"${AppConstants.appData.userReference}"'}
      ) {
        visited
      }
    }
  ''';

    //#region Region - Execute Request
    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);

    // // var body = {
    // //   "post_reference": postReference,
    // //   "liked_by_user": AppConstants.appData.isUserView! ? AppConstants.appData.userReference : "",
    // //   "liked_by_store": AppConstants.appData.isStoreView! ? AppConstants.appData.storeReference : ""
    // // };
    // //#region Region - Execute Request
    // response = await httpService.postApiCall(body, AppConstants.likeDisLikePost);
    // return response['data']['is_liked'];
    // return PostDetail.fromJson(response['data']['is_liked']);
    return response["data"]["addVisit"]["visited"];
  }

// endregion

  ///Comment

  //region Get comment access detail
  Future<CommentAccessDetails> getCommentAccessDetail({
    required String productReference,
    required String reference,
  }) async {
    // Get body [for POST request]
    var body = {
      "product_reference": productReference,
      "entity_reference": AppConstants.appData.isUserView!
          ? AppConstants.appData.userReference!
          : AppConstants.appData.storeReference!,
    };
    debugPrint(body.toString());

    debugPrint(AppConstants.getCommentOptions);
    // Execute Request
    Map<String, dynamic> response =
        await httpService.postApiCall(body, AppConstants.getCommentOptions);

    debugPrint(response.toString());
    // Extract the access_details from the response and parse it into CommentAccessDetails
    // var accessDetailsJson = response['access_details'];
    CommentAccessDetails accessDetails =
        CommentAccessDetails.fromJson(response);

    return accessDetails;
  }

  //endregion

  //region Get comment list
  Future<List<PostDetail>> getCommentList(
      {required int limit,
      required int offset,
      required String parentReference}) async {
    // endregion
    Map<String, dynamic> response;

    // Query
    var query = '''
  query GetChildrenComments {
    getChildrenComments(parentReference:"${parentReference}",
     limit: $limit, offset: $offset) {
      likeCount
        commentCount
        repostCount
        repostPlusCount
        saveCount
        shareCount
        ratingCount
        createdDate
        likeStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
        repostStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
        saveStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
        commentText
        reference
        level
        commentType
        mainParentId
        parentCommentId
        isDeleted
        commentHeaderText
        createdBy {
            reference
            entityType
            handle
            name
            icon
            phoneNumber
            pincode
            city
            isDeleted
            createdDate
            categoryName
            followersOrSupportersCount
            followingOrSupportingCount
            followStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
        }
        commentHeaders {
            handle
            reference
        }
        commentImages {
            mediaId
            mediaType
            mediaPath
            order
        }
        parentHandle
    }
}


  ''';

    //#region Region - Execute Request
    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);

    // return response;
    // Parse the response and extract the list of PostDetail instances
    List<PostDetail> postDetails = [];

    if (response.containsKey('data') &&
        response['data'] != null &&
        response['data']['getChildrenComments'] != null) {
      List<dynamic> postsJson = response['data']['getChildrenComments'];
      postDetails = postsJson.map((json) => PostDetail.fromJson(json)).toList();
    }
    //print(response);
    return postDetails;
  }

// endregion

  //region Get single comment
  Future<PostDetail> getSingleComment(
      {required String commentReference}) async {
    // endregion
    Map<String, dynamic> response;

    // Query
    var query = '''
  query GetComment {
    getComment(commentReference:"${commentReference}") {
      likeCount
      ratingCount
        commentCount
        repostCount
        repostPlusCount
        saveCount
        shareCount
        createdDate
        likeStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
        repostStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
        saveStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
        commentText
        reference
        level
        commentType
        mainParentId
        parentCommentId
        isDeleted
        commentHeaderText
        createdBy {
            reference
            entityType
            handle
            name
            icon
            phoneNumber
            pincode
            city
            isDeleted
            createdDate
            categoryName
            followersOrSupportersCount
            followingOrSupportingCount
            followStatus(visitorReference: "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}")
        }
        commentHeaders {
            handle
            reference
        }
        commentImages {
            mediaId
            mediaType
            mediaPath
            order
        }
        parentHandle
    }
}


  ''';

    //#region Region - Execute Request
    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);

    // return response;
    // Parse the response and extract the list of PostDetail instances
    late PostDetail postDetails;

    Map<String, dynamic> commentJson = response['data']['getComment'];
    postDetails = PostDetail.fromJson(commentJson);
    // Parse the response and extract the PostDetail instance
    // if (response.containsKey('data') && response['getComment'] != null) {
    //   Map<String, dynamic> commentJson = response['data']['getComment'];
    //   postDetails = PostDetail.fromJson(commentJson);
    // }
    return postDetails;
  }

// endregion

  // region Delete comment
  Future<bool> deleteComment({required String postRefrence}) async {
    // endregion
    Map<String, dynamic> response;
    // Query
    var query = '''
 mutation DeleteComment {
    deleteComment(commentReference: "$postRefrence") {
        success
    }
}

  ''';

    response = await httpService.postApiCall(
        {'query': query.replaceAll("\n", "")}, AppConstants.graphQlUrl);

    // Parse the response and extract the list of PostDetail instances
    bool postDeleteStatus = false;

    if (response.containsKey('data') &&
        response['data'] != null &&
        response['data']['deleteComment'] != null) {
      postDeleteStatus = response['data']?['deleteComment']?['success'];
    }
    //print(response);
    return postDeleteStatus;
  }

// endregion

  //region Get lean feed
  Future<List<dynamic>> getLeanFeeds(
      {required int limit,
      required int offset,
      required BuildContext context}) async {
    //Get app data from cache
    await AppDataService().getAppData();

    // Get user reference and pincode for API call
    String visitorReference = AppConstants.appData.isUserView!
        ? AppConstants.appData.userReference!
        : AppConstants.appData.storeReference!;
    String userPincode = AppConstants.appData.pinCode ??
        "524004"; // Default pincode if not available

    // Construct the URL for the lean feed API
    String url =
        "${AppConstants.baseUrl}/lean/feed/?limit=$limit&offset=$offset&visitor_reference=$visitorReference&user_pincode=$userPincode";

    debugPrint("Calling lean feed API: $url");

    // Execute the API call
    Map<String, dynamic> response = await httpService.getApiCall(url);

    // Parse the response and extract the list of items
    List<dynamic> feedItems = [];

    if (response.containsKey('message') &&
        response['message'] == 'success' &&
        response['data'] != null) {
      List<dynamic> dataJson = response['data'];

      // Process each item in the response
      feedItems = dataJson.map((json) {
        // Check if the item is a post or product based on available fields
        if (json.containsKey('post_reference')) {
          // It's a post
          return PostDetail.fromLeanJson(json);
        } else if (json.containsKey('product_reference')) {
          // It's a product
          return Product.fromLeanJson(json);
        }
        // Unknown type
        throw Exception('Unknown content type in lean feed response');
      }).toList();
    }

    debugPrint("Lean feed API returned ${feedItems.length} items");
    return feedItems;
  }
  // endregion

  //region Get lean all feed
  Future<List<dynamic>> getLeanAllFeeds(
      {required int limit,
      required int offset,
      required BuildContext context}) async {
    //Get app data from cache
    await AppDataService().getAppData();

    // Get user reference and pincode for API call
    String visitorReference = AppConstants.appData.isUserView!
        ? AppConstants.appData.userReference!
        : AppConstants.appData.storeReference!;
    String userPincode = AppConstants.appData.pinCode ??
        "524004"; // Default pincode if not available

    // Construct the URL for the lean all feed API
    String url =
        "${AppConstants.baseUrl}/lean/all_feed/?limit=$limit&offset=$offset&visitor_reference=$visitorReference&user_pincode=$userPincode";

    debugPrint("Calling lean all feed API: $url");

    // Execute the API call
    Map<String, dynamic> response = await httpService.getApiCall(url);

    // Parse the response and extract the list of items
    List<dynamic> feedItems = [];

    if (response.containsKey('message') &&
        response['message'] == 'success' &&
        response['data'] != null) {
      List<dynamic> dataJson = response['data'];

      // Process each item in the response
      feedItems = dataJson.map((json) {
        // Check if the item is a post or product based on available fields
        if (json.containsKey('post_reference')) {
          // It's a post
          return PostDetail.fromLeanJson(json);
        } else if (json.containsKey('product_reference')) {
          // It's a product
          return Product.fromLeanJson(json);
        }
        // Unknown type
        throw Exception('Unknown content type in lean all feed response');
      }).toList();
    }

    debugPrint("Lean all feed API returned ${feedItems.length} items");
    return feedItems;
  }
  // endregion

  // region Get Post Tagged Object Details
  Future<PostTaggedObjectsResponse> getPostTaggedObjectDetails({
    required String postReference,
  }) async {
    //Get app data from cache
    await AppDataService().getAppData();

    // Construct the URL for the API
    String url = "${AppConstants.getPostTaggedObjectDetails}?post_reference=$postReference";

    debugPrint("Calling get post tagged object details API: $url");

    // Execute the API call
    Map<String, dynamic> response = await httpService.getApiCall(url);

    debugPrint("Get post tagged object details API response: $response");

    // Parse the response
    return PostTaggedObjectsResponse.fromJson(response);
  }
  // endregion

  // region Get Tagged Posts to Content
  Future<List<PostDetail>> getTaggedPostsToContent({
    required String contentReference,
    required int limit,
    required int offset,
  }) async {
    //Get app data from cache
    await AppDataService().getAppData();

    // Get visitor reference for API call
    String visitorReference = AppConstants.appData.isUserView!
        ? AppConstants.appData.userReference!
        : AppConstants.appData.storeReference!;

    // Construct the URL for the API
    String url = "${AppConstants.getTaggedPostDetails}?limit=$limit&offset=$offset&visitor_reference=$visitorReference&content_reference=$contentReference";

    debugPrint("Calling get tagged posts to content API: $url");

    // Execute the API call
    Map<String, dynamic> response = await httpService.getApiCall(url);

    debugPrint("Get tagged posts to content API response: $response");

    // Parse the response and extract the list of posts
    List<PostDetail> postDetails = [];

    if (response.containsKey('message') &&
        response['message'] == 'success' &&
        response['data'] != null) {
      List<dynamic> dataJson = response['data'];

      // Process each post in the response using fromLeanJson
      postDetails =
          dataJson.map((json) => PostDetail.fromLeanJson(json)).toList();
    }

    debugPrint(
        "Get tagged posts to content API returned ${postDetails.length} posts");
    return postDetails;
  }
  // endregion
}
