import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/product_label_dropdown/product_label_dropdown_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/product_label_dropdown/product_label_dropdown_common_widget.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Product label dropdown
class ProductLabelDropdown extends StatefulWidget {
  final Product product;
  final double padding;

  const ProductLabelDropdown(
      {super.key, required this.product, this.padding = 16});

  @override
  State<ProductLabelDropdown> createState() => _ProductLabelDropdownState();
}
//endregion

class _ProductLabelDropdownState extends State<ProductLabelDropdown> {
  //region Bloc
  late ProductLabelDropdownBloc productLabelDropdownBloc;

  //endregion

  //region Init
  @override
  void initState() {
    productLabelDropdownBloc =
        ProductLabelDropdownBloc(context, widget.product);
    productLabelDropdownBloc.init();
    super.initState();
  }

  //endregion

  //region Did update
  @override
  void didUpdateWidget(covariant ProductLabelDropdown oldWidget) {
    productLabelDropdownBloc.product = widget.product;
    productLabelDropdownBloc.refreshLabelCtrl.sink.add(true);
    super.didUpdateWidget(oldWidget);
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<bool>(
        stream: productLabelDropdownBloc.refreshLabelCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InkWell(
                onTap: () {
                  productLabelDropdownBloc.onTapDropDown();
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: widget.padding),
                  alignment: Alignment.centerLeft,
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(
                        AppImages.swadeshiLabelsIcon2,
                        height: 28,
                        width: 28,
                      ),
                      horizontalSizedBox(5),
                      Text(
                        AppStrings.swadeshiLabel,
                        style: AppTextStyle.contentHeading0(
                                textColor: AppColors.appBlack)
                            .copyWith(height: 1.2),
                      ),
                      horizontalSizedBox(5),
                      RotatedBox(
                        quarterTurns:
                            productLabelDropdownBloc.isDropDownVisible ? 2 : 0,
                        child: SvgPicture.asset(
                          AppImages.arrow,
                          height: 30,
                          width: 30,
                        ),
                      ),
                      const Spacer(),
                      taggedPosts()
                    ],
                  ),
                ),
              ),
              Visibility(
                  visible: productLabelDropdownBloc.isDropDownVisible,
                  child: labelList())
            ],
          );
        });
  }

//endregion

//region Label list
  Widget labelList() {
    return ListView(
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      shrinkWrap: true,
      children: [
        verticalSizedBox(10),
        //Made
        InkWell(
            onTap: () {
              productLabelDropdownBloc.onTapLabels(
                  bottomSheetName: CommonMethods.labelStatusToSentence(
                          input:
                              productLabelDropdownBloc.product.swadeshiMade) ??
                      "",
                  isMade: true);
            },
            child: ProductLabelInfoCommonWidgets.label(
              title: AppStrings.made,
              buttonName: CommonMethods.labelStatusToSentence(
                      input: productLabelDropdownBloc.product.swadeshiMade) ??
                  "",
              context: context,
              borderColor: Color(0xFFFF671F), // Orange border
              fillColor: Color(0xFFFF671F)
                  .withOpacity(0.15), // 15% opacity orange fill
            )),
        verticalSizedBox(10),

        //Brand
        InkWell(
          onTap: () {
            productLabelDropdownBloc.onTapLabels(
                bottomSheetName: CommonMethods.labelStatusToSentence(
                        input:
                            productLabelDropdownBloc.product.swadeshiBrand) ??
                    "",
                isBrand: true);
          },
          child: ProductLabelInfoCommonWidgets.label(
            title: AppStrings.brand,
            buttonName: CommonMethods.labelStatusToSentence(
                    input: productLabelDropdownBloc.product.swadeshiBrand) ??
                "",
            context: context,
            borderColor: Color(0xFF06038D), // Blue border
            fillColor:
                Color(0xFF06038D).withOpacity(0.15), // 15% opacity blue fill
          ),
        ),
        verticalSizedBox(10),

        //Owned
        InkWell(
            onTap: () {
              productLabelDropdownBloc.onTapLabels(
                  bottomSheetName: CommonMethods.labelStatusToSentence(
                          input:
                              productLabelDropdownBloc.product.swadeshiOwned) ??
                      "",
                  isOwned: true);
            },
            child: ProductLabelInfoCommonWidgets.label(
              title: AppStrings.owned,
              buttonName: CommonMethods.labelStatusToSentence(
                      input: productLabelDropdownBloc.product.swadeshiOwned) ??
                  "",
              context: context,
              borderColor: Color(0xFF046A38), // Green border
              fillColor:
                  Color(0xFF046A38).withOpacity(0.15), // 15% opacity green fill
            )),
        verticalSizedBox(10),
      ],
    );
  }
//endregion


//region Tagged posts
  Widget taggedPosts() {
    final totalPosts = productLabelDropdownBloc.product.totalTaggedPosts ?? 0;
    final postImages = (productLabelDropdownBloc.product.taggedPostImages ?? []).take(3).toList();
    
    return Visibility(
      visible: totalPosts > 0,
      child: CupertinoButton(
        minSize: 15,
        padding: EdgeInsets.zero,
        onPressed: () {
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: postImages.isEmpty ? 0 : 20 + (postImages.length - 1) * 15.0,
              height: 20,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // Display up to 3 circular images with slight overlap
                  ...List.generate(
                    postImages.length,
                    (index) => Positioned(
                      left: index * 15.0,
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white,
                            width: 1.5,
                          ),
                          color: Colors.grey[200], // Fallback color
                        ),
                        child: ClipOval(
                          child: postImages[index]['image_path'] != null
                              ? Image.network(
                                  '${AppConstants.baseUrl}${postImages[index]['image_path']}',
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) => 
                                      Icon(Icons.error, size: 16, color: Colors.grey),
                                )
                              : Icon(Icons.photo, size: 12, color: Colors.grey),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (postImages.isNotEmpty) const SizedBox(width: 8),
            Text(
              "+$totalPosts posts",
              style: AppTextStyle.contentHeading0(
                textColor: AppColors.appBlack,
              ).copyWith(height: 1.2),
            ),
          ]
      ),
    )
    );
  }
//endregion
}
