# Lean APIs

## get_store_products_with_limited_details

### Curl
curl --location 'https://e2e-77-175.ssdcloudindia.net/dev/lean/store/S1744452089786/products/?limit=10&offset=0' \
--header 'Content-Type: application/json' \
--header 'Accept: application/json' \
--header 'Authorization: Bearer <token>'

### Response
```json
{
    "message": "success",
    "data": [
        {
            "product_reference": "P1745875065560581OMHU",
            "product_name": "planets",
            "brand_name": "Kitten kreeps",
            "product_category": "planets",
            "mrp_price": 2500,
            "selling_price": 2000,
            "product_images": [
                {
                    "productimageid": 310,
                    "reorder": 1,
                    "is_deleted": false,
                    "product_image": "/media/post_images/STScI-01EVSVE3VNSYF8J7J7AZ4P6NB8.jpg"
                },
                {
                    "productimageid": 311,
                    "reorder": 2,
                    "is_deleted": false,
                    "product_image": "/media/post_images/STScI-01EVST70HNTJNZ5V6SDJVKB5Q2.png"
                },
                {
                    "productimageid": 312,
                    "reorder": 3,
                    "is_deleted": false,
                    "product_image": "/media/post_images/STScI-01EVSSW78ZSG7KP71HMSDFM5MH.png"
                },
                {
                    "productimageid": 313,
                    "reorder": 4,
                    "is_deleted": false,
                    "product_image": "/media/post_images/STScI-01EVSSWGY8JTJ8MMBYTC0QR68F.png"
                }
            ]
        },
        {
            "product_reference": "P1745870510016538IKHQ",
            "product_name": "Eyes pics",
            "brand_name": "Kitten kreeps",
            "product_category": "Ai Images",
            "mrp_price": 2500,
            "selling_price": 2000,
            "product_images": [
                {
                    "productimageid": 305,
                    "reorder": 1,
                    "is_deleted": false,
                    "product_image": "/media/post_images/1745870417894.jpg"
                },
                {
                    "productimageid": 306,
                    "reorder": 2,
                    "is_deleted": false,
                    "product_image": "/media/post_images/1745870417894_dAM4vHB.jpg"
                },
                {
                    "productimageid": 307,
                    "reorder": 3,
                    "is_deleted": false,
                    "product_image": "/media/post_images/1745870417894_ERi5vY0.jpg"
                },
                {
                    "productimageid": 308,
                    "reorder": 4,
                    "is_deleted": false,
                    "product_image": "/media/post_images/1745870417894_01ftVoi.jpg"
                },
                {
                    "productimageid": 309,
                    "reorder": 5,
                    "is_deleted": false,
                    "product_image": "/media/post_images/1745870417894_OGb9eOE.jpg"
                }
            ]
        }
    ]
}
```

## get_product_full_details
### Curl
```bash
curl --location 'http://***********:8000/lean/products/full_details/' \
--header 'Content-Type: application/json' \
--header 'Accept: application/json' \
--header 'Authorization: Bearer <token>' \
--data '{
    "product_references": ["P1745751358688947FVTS", "P1745237043414279NBRH"],
    "visitor_reference": "U1744450273600",
    "user_pincode": "524004"
  }'
```

### Response
```json
{
    "message": "success",
    "data": [
        {
            "productid": 5,
            "product_reference": "P1745751358688947FVTS",
            "product_name": "Ryomen Sukuna Eye",
            "product_description": "Very detailed and awesome",
            "product_category": "Html pages",
            "product_version": "1.0.0",
            "updated_date": "27:04:2025 16:25:58",
            "created_date": "27:04:2025 16:25:58",
            "promotion_link": "https://www.ryomensukuna.in",
            "brand_name": "Hanuman Coders",
            "mrp_price": 250,
            "selling_price": 200,
            "count_of_ratings": 0,
            "rating": null,
            "in_stock": 9999,
            "hashtags": "#jujutsukaisen",
            "targeted_gender": "U",
            "store_reference": "S1744452089786",
            "storeid": 4,
            "store_icon": "/media/store_icons/er_1744452012018.jpg",
            "store_name": "Hanuman Coders",
            "storehandle": "hanuman_coders",
            "swadeshi_brand": "FULLY_SWADESHI_BRAND",
            "swadeshi_made": "FULLY_SWADESHI_MADE",
            "deleted": false,
            "is_test_store": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "orders_count": 0,
            "returns_count": 0,
            "cancels_count": 0,
            "prod_images": [
                {
                    "productimageid": 5,
                    "reorder": 1,
                    "is_deleted": false,
                    "product_image": "/media/post_images/1745751227498.jpg"
                }
            ],
            "saved_or_not": false,
            "location": "Nellore",
            "swadeshi_owned": "FULLY_SWADESHI_OWNED",
            "return_period": 3,
            "return_conditions": [
                "Unbowed, unbent ",
                " off",
                " unbroken",
                "Unused",
                "Fajg"
            ],
            "return_pick_up": "seller",
            "return_cost_on": "SELLER",
            "refund_responsibility": [
                {
                    "item_heading": "Refund policy",
                    "item_text": "Full refund based on conditions",
                    "item_subtext": ""
                },
                {
                    "item_heading": "If the seller cancels or if the order is auto-canceled",
                    "item_text": "Full Refund",
                    "item_subtext": ""
                },
                {
                    "item_heading": "If the customer cancels before order confirmation",
                    "item_text": "Partial Refund",
                    "item_subtext": "Transaction fee charged by payment gateway (usually 2% + 18% GST if any) will be reduced from the refund amount"
                },
                {
                    "item_heading": "On product returns",
                    "item_text": "Full Refund",
                    "item_subtext": ""
                }
            ],
            "delivery_settings_type": "store_settings",
            "delivery_by": 2,
            "delivery_partner": "self",
            "delivery_fee": "₹50 per store order",
            "logistic_partner_name": null,
            "fulfillment_options": "DELIVERY",
            "deliverability": false,
            "config_receive_orders": true,
            "is_buy_enabled": false,
            "product_status_message": null,
            "like_status": false,
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "content_headers": [],
            "content_header_text": null,
            "disclaimer_message": "Shop with care|This new store is not yet public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual."
        },
        {
            "productid": 4,
            "product_reference": "P1745237043414279NBRH",
            "product_name": "Kumaresan HTML",
            "product_description": "Ghibli style art",
            "product_category": "Html page",
            "product_version": "1.0.0",
            "updated_date": "21:04:2025 17:34:03",
            "created_date": "21:04:2025 17:34:03",
            "promotion_link": "https://www.kumaresanhtml.com",
            "brand_name": "Hanuman coders",
            "mrp_price": 1050,
            "selling_price": 1000,
            "count_of_ratings": 0,
            "rating": null,
            "in_stock": 9986,
            "hashtags": "#kumaresan",
            "targeted_gender": "U",
            "store_reference": "S1744452089786",
            "storeid": 4,
            "store_icon": "/media/store_icons/er_1744452012018.jpg",
            "store_name": "Hanuman Coders",
            "storehandle": "hanuman_coders",
            "swadeshi_brand": "FULLY_SWADESHI_BRAND",
            "swadeshi_made": "FULLY_SWADESHI_MADE",
            "deleted": false,
            "is_test_store": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "orders_count": 0,
            "returns_count": 0,
            "cancels_count": 0,
            "prod_images": [
                {
                    "productimageid": 4,
                    "reorder": 1,
                    "is_deleted": false,
                    "product_image": "/media/post_images/caled_1000118133.jpg"
                }
            ],
            "saved_or_not": false,
            "location": "Nellore",
            "swadeshi_owned": "FULLY_SWADESHI_OWNED",
            "return_period": 3,
            "return_conditions": [
                "Unbowed, unbent ",
                " off",
                " unbroken",
                "Unused",
                "Fajg"
            ],
            "return_pick_up": "seller",
            "return_cost_on": "SELLER",
            "refund_responsibility": [
                {
                    "item_heading": "Refund policy",
                    "item_text": "Full refund based on conditions",
                    "item_subtext": ""
                },
                {
                    "item_heading": "If the seller cancels or if the order is auto-canceled",
                    "item_text": "Full Refund",
                    "item_subtext": ""
                },
                {
                    "item_heading": "If the customer cancels before order confirmation",
                    "item_text": "Partial Refund",
                    "item_subtext": "Transaction fee charged by payment gateway (usually 2% + 18% GST if any) will be reduced from the refund amount"
                },
                {
                    "item_heading": "On product returns",
                    "item_text": "Full Refund",
                    "item_subtext": ""
                }
            ],
            "delivery_settings_type": "store_settings",
            "delivery_by": 2,
            "delivery_partner": "self",
            "delivery_fee": "₹50 per store order",
            "logistic_partner_name": null,
            "fulfillment_options": "DELIVERY",
            "deliverability": false,
            "config_receive_orders": true,
            "is_buy_enabled": false,
            "product_status_message": null,
            "like_status": false,
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "content_headers": [],
            "content_header_text": null,
            "disclaimer_message": "Shop with care|This new store is not yet public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual."
        }
    ]
}
```


## get_product_partial_details

### Curl
```bash
curl --location 'http://***********:8000/lean/products/partial_details/' \
--header 'Content-Type: application/json' \
--header 'Accept: application/json' \
--header 'Authorization: Bearer <token>' \
--data '{
    "product_references": ["P1745751358688947FVTS", "P1745237043414279NBRH"],
    "visitor_reference": "U1744450273600",
    "user_pincode": "524004"
  }'
```

### Response
```json
{
    "message": "success",
    "data": [
        {
            "productid": 5,
            "product_reference": "P1745751358688947FVTS",
            "store_reference": "S1744452089786",
            "store_icon": "/media/store_icons/er_1744452012018.jpg",
            "storehandle": "hanuman_coders",
            "product_name": "Ryomen Sukuna Eye",
            "brand_name": "Hanuman Coders",
            "product_description": "Very detailed and awesome",
            "mrp_price": 250,
            "selling_price": 200,
            "swadeshi_brand": "FULLY_SWADESHI_BRAND",
            "swadeshi_made": "FULLY_SWADESHI_MADE",
            "like_count": 0,
            "comment_count": 0,
            "product_images": [
                {
                    "productimageid": 5,
                    "reorder": 1,
                    "is_deleted": false,
                    "product_image": "/media/post_images/1745751227498.jpg"
                }
            ],
            "like_status": false,
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "content_headers": [],
            "content_header_text": null,
            "is_buy_enabled": true,
            "product_status_message": null
        },
        {
            "productid": 4,
            "product_reference": "P1745237043414279NBRH",
            "store_reference": "S1744452089786",
            "store_icon": "/media/store_icons/er_1744452012018.jpg",
            "storehandle": "hanuman_coders",
            "product_name": "Kumaresan HTML",
            "brand_name": "Hanuman coders",
            "product_description": "Ghibli style art",
            "mrp_price": 1050,
            "selling_price": 1000,
            "swadeshi_brand": "FULLY_SWADESHI_BRAND",
            "swadeshi_made": "FULLY_SWADESHI_MADE",
            "like_count": 0,
            "comment_count": 0,
            "product_images": [
                {
                    "productimageid": 4,
                    "reorder": 1,
                    "is_deleted": false,
                    "product_image": "/media/post_images/caled_1000118133.jpg"
                }
            ],
            "like_status": false,
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "content_headers": [],
            "content_header_text": null,
            "is_buy_enabled": true,
            "product_status_message": null
        }
    ]
}
```


## get lean feed api 

### Curl
```bash
curl --location 'http://***********:8000/lean/feed/?limit=100&offset=0&visitor_reference=U1744450273600&user_pincode=524004' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>'
```

### Response
```json
{
    "message": "success",
    "data": [
        {
            "post_reference": "PO202505101357189186",
            "post_id": 25,
            "quote_parent_id": null,
            "user_reference": "U1744450273600",
            "store_reference": null,
            "post_text": "Test post to check pagination 23, Jai Shree Ram. Thermodynamics is the branch of physics that studies the relationships between heat, energy, and work. It describes how energy moves and transforms in systems, particularly in terms of heat transfer and mechanical work. It is fundamental to understanding engines, refrigeration, and many natural and industrial processes.",
            "created_date": "2025-05-10 13:57:18.147126+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "post_images": [
                {
                    "post_image_id": "496bcff2-88d3-4938-8436-8124192f3a3d",
                    "post_image": "/media/social_post_images/Gemini_Generated_Image_7tdbq47tdbq47tdb_2q2DUZv.jpeg",
                    "created_date": "2025-05-10T13:57:18.297409+05:30",
                    "is_deleted": false,
                    "reorder": 1
                }
            ],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_header_text": "",
            "content_headers": [],
            "content_type": "POST",
            "icon": "/media/profile_image/r_1744450383001.jpg",
            "handle": "krishna_k",
            "name": "Krishna Kanth ",
            "reference": "U1744450273600"
        },
        {
            "productid": 3730,
            "product_reference": "P1746866852344796TSCF",
            "product_name": "Test Product 49",
            "product_description": "HTML anime page 49",
            "product_category": "Anime",
            "product_version": "1.0.0",
            "updated_date": "10:05:2025 14:17:32",
            "created_date": "10:05:2025 14:17:32",
            "promotion_link": "www.haumancodersAnime49",
            "brand_name": "Hanuman Coders",
            "mrp_price": 1200,
            "selling_price": 1000,
            "count_of_ratings": 0,
            "rating": null,
            "in_stock": 1000,
            "hashtags": "#haumancodersAnime49",
            "store_reference": "S1744452089786",
            "storeid": 4,
            "targeted_gender": "U",
            "swadeshi_brand": null,
            "swadeshi_made": null,
            "deleted": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "orders_count": 0,
            "returns_count": 0,
            "cancels_count": 0,
            "modified_date": "2025-05-10 14:17:32.350788+05:30",
            "promotion_amount": 0,
            "is_promotion_enabled": false,
            "store_icon": "store_icons/er_1744452012018.jpg",
            "store_name": "Hanuman Coders",
            "storehandle": "hanuman_coders",
            "is_test_store": false,
            "subscription_type": "FREE",
            "open_for_order": true,
            "store_valuation": 0.0,
            "store_level": "1",
            "location": "Nellore",
            "swadeshi_owned": "FULLY_SWADESHI_OWNED",
            "prod_images": [
                {
                    "productimageid": 60,
                    "product_image": "post_images/1744453108520.jpg",
                    "created_date": "2025-05-10T14:30:39.833291+05:30",
                    "is_deleted": false,
                    "reorder": 1
                },
                {
                    "productimageid": 115,
                    "product_image": "post_images/caled_1000118133.jpg",
                    "created_date": "2025-05-10T14:32:06.23455+05:30",
                    "is_deleted": false,
                    "reorder": 1
                },
                {
                    "productimageid": 170,
                    "product_image": "post_images/garg.jpg",
                    "created_date": "2025-05-10T14:34:53.177382+05:30",
                    "is_deleted": false,
                    "reorder": 1
                },
                {
                    "productimageid": 225,
                    "product_image": "post_images/dp.jpg",
                    "created_date": "2025-05-10T14:35:18.714052+05:30",
                    "is_deleted": false,
                    "reorder": 1
                }
            ],
            "delivery_settings_type": "store_settings",
            "delivery_by": 2,
            "delivery_partner": "logistics",
            "delivery_fee": "₹50 per store order",
            "logistic_partner_name": "Shiprocket",
            "fulfillment_options": "DELIVERY",
            "return_settings_type": "store_settings",
            "return_period": null,
            "return_conditions": null,
            "return_pick_up": "seller",
            "return_cost_on": "SELLER",
            "refund_responsibility": [
                {
                    "item_text": "Full refund based on conditions",
                    "item_heading": "Refund policy",
                    "item_subtext": ""
                },
                {
                    "item_text": "Full Refund",
                    "item_heading": "If the seller cancels or if the order is auto-canceled",
                    "item_subtext": ""
                },
                {
                    "item_text": "Partial Refund",
                    "item_heading": "If the customer cancels before order confirmation",
                    "item_subtext": "Transaction fee charged by payment gateway (usually 2% + 18% GST if any) will be reduced from the refund amount"
                },
                {
                    "item_text": "Full Refund",
                    "item_heading": "On product returns",
                    "item_subtext": ""
                }
            ],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_header_text": "",
            "content_headers": [],
            "content_type": "PRODUCT",
            "icon": "/media/store_icons/er_1744452012018.jpg",
            "handle": "hanuman_coders",
            "name": "Hanuman Coders",
            "reference": "S1744452089786",
            "deliverability": true,
            "is_buy_enabled": true,
            "product_status_message": null,
            "config_receive_orders": true,
            "disclaimer_message": "Shop with care|This new store is not yet public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual."
        }
    ]
}
```


## Get lean all feed api

### Curl
```bash
curl --location 'https://e2e-77-175.ssdcloudindia.net/dev/lean/all_feed/?limit=100&offset=0&visitor_reference=U1719579800140&user_pincode=524004' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>'
```

### Response
```json
{
    "message": "success",
    "data": [
        {
            "post_reference": "PO202504290313450126",
            "post_id": 2328,
            "quote_parent_id": null,
            "user_reference": "U1719579800140",
            "store_reference": null,
            "post_text": "hello",
            "created_date": "2025-04-29 03:13:45.402253+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 2,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "post_images": [
                {
                    "post_image_id": "8ff08771-9dc1-4122-91a0-6c956ad588db",
                    "post_image": "/media/social_post_images/Screenshot%20(226)_hMjRyM7.jpg",
                    "created_date": "2025-04-29T03:13:45.623979+05:30",
                    "is_deleted": false,
                    "reorder": 1
                }
            ],
            "save_status": false,
            "repost_status": false,
            "like_status": false,
            "content_category": "POST",
            "content_header_text": "",
            "content_headers": [],
            "content_type": "POST",
            "icon": "/media/profile_image/6399598.jpg",
            "handle": "krishna_k",
            "name": "Krishna kanth",
            "reference": "U1719579800140"
        },
                {
            "productid": 105,
            "product_reference": "P1745875065560581OMHU",
            "product_name": "planets",
            "product_description": "HD 4k quality",
            "product_category": "planets",
            "product_version": "4.0.0",
            "updated_date": "06:05:2025 16:22:35",
            "created_date": "29:04:2025 02:47:45",
            "promotion_link": "https://www.planets.com",
            "brand_name": "Kitten kreeps",
            "mrp_price": 2500,
            "selling_price": 2000,
            "count_of_ratings": 0,
            "rating": null,
            "in_stock": 100000,
            "hashtags": "#planets",
            "store_reference": "S1721930951916",
            "storeid": 10,
            "targeted_gender": "U",
            "swadeshi_brand": "FULLY_SWADESHI_BRAND",
            "swadeshi_made": "FULLY_SWADESHI_MADE",
            "deleted": false,
            "like_count": 1,
            "comment_count": 1,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "orders_count": 0,
            "returns_count": 0,
            "cancels_count": 0,
            "modified_date": "2025-05-06 16:22:35.146161+05:30",
            "promotion_amount": 0,
            "is_promotion_enabled": false,
            "store_icon": "store_icons/wallpaperflare.com_wallpaper (3).jpg",
            "store_name": "Kittenkreeps",
            "storehandle": "kitten_kreeps",
            "is_test_store": false,
            "subscription_type": "FREE",
            "open_for_order": true,
            "store_valuation": 0,
            "store_level": "1",
            "location": "Nellore",
            "swadeshi_owned": "FULLY_SWADESHI_OWNED",
            "prod_images": [
                {
                    "productimageid": 310,
                    "product_image": "post_images/STScI-01EVSVE3VNSYF8J7J7AZ4P6NB8.jpg",
                    "created_date": "2025-04-29T02:47:46.430308+05:30",
                    "is_deleted": false,
                    "reorder": 1
                },
                {
                    "productimageid": 311,
                    "product_image": "post_images/STScI-01EVST70HNTJNZ5V6SDJVKB5Q2.png",
                    "created_date": "2025-04-29T02:47:47.185823+05:30",
                    "is_deleted": false,
                    "reorder": 2
                },
                {
                    "productimageid": 312,
                    "product_image": "post_images/STScI-01EVSSW78ZSG7KP71HMSDFM5MH.png",
                    "created_date": "2025-04-29T02:47:49.631864+05:30",
                    "is_deleted": false,
                    "reorder": 3
                },
                {
                    "productimageid": 313,
                    "product_image": "post_images/STScI-01EVSSWGY8JTJ8MMBYTC0QR68F.png",
                    "created_date": "2025-04-29T02:47:53.191827+05:30",
                    "is_deleted": false,
                    "reorder": 4
                }
            ],
            "delivery_settings_type": "store_settings",
            "delivery_by": 4,
            "delivery_partner": "self",
            "delivery_fee": "free",
            "logistic_partner_name": null,
            "fulfillment_options": "IN_STORE_PICKUP",
            "return_settings_type": "store_settings",
            "return_period": 7,
            "return_conditions": [
                "No damage"
            ],
            "return_pick_up": "seller",
            "return_cost_on": "SELLER",
            "refund_responsibility": [
                {
                    "item_text": "Full refund based on conditions",
                    "item_heading": "Refund policy",
                    "item_subtext": ""
                },
                {
                    "item_text": "Full Refund",
                    "item_heading": "If the seller cancels or if the order is auto-canceled",
                    "item_subtext": ""
                },
                {
                    "item_text": "Partial Refund",
                    "item_heading": "If the customer cancels before order confirmation",
                    "item_subtext": "Transaction fee charged by payment gateway (usually 2% + 18% GST if any) will be reduced from the refund amount"
                },
                {
                    "item_text": "Full Refund",
                    "item_heading": "On product returns",
                    "item_subtext": ""
                }
            ],
            "save_status": false,
            "repost_status": false,
            "like_status": true,
            "content_category": "POST",
            "content_header_text": "",
            "content_headers": [],
            "content_type": "PRODUCT",
            "icon": "/media/store_icons/wallpaperflare.com_wallpaper%20(3).jpg",
            "handle": "kitten_kreeps",
            "name": "Kittenkreeps",
            "reference": "S1721930951916",
            "deliverability": false,
            "is_buy_enabled": false,
            "product_status_message": "Not deliverable to your pincode",
            "config_receive_orders": true,
            "disclaimer_message": "Shop with care|This new store is not yet public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual."
        }
    ]

}
```     


## Get user or store Posts

### Curl
```bash
curl --location 'http://192.168.39.44:8000/lean/get_user_or_store_posts/?limit=100&offset=0&visitor_reference=U1744450273600&entity_reference=S1744452089786' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>'
```

### Response
```json
{
    "message": "success",
    "data": [
        {
            "post_reference": "PO202505101400414809",
            "post_id": 102,
            "quote_parent_id": null,
            "user_reference": null,
            "store_reference": "S1744452089786",
            "post_text": "Test post to check pagination 50, Jai Shree Ram. Thermodynamics is the branch of physics that studies the relationships between heat, energy, and work. It describes how energy moves and transforms in systems, particularly in terms of heat transfer and mechanical work. It is fundamental to understanding engines, refrigeration, and many natural and industrial processes.",
            "created_date": "2025-05-10 14:00:41.513888+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "post_images": [
                {
                    "post_image_id": "edbb0f8f-5ccb-4f9b-847c-2250c89af967",
                    "post_image": "/media/social_post_images/wp4678876-dragon-ball-super-4k-wallpapers_L5WkTUG.jpg",
                    "created_date": "2025-05-10T14:00:41.85204+05:30",
                    "is_deleted": false,
                    "reorder": 1
                }
            ],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_header_text": "",
            "content_headers": [],
            "content_type": "POST",
            "icon": "/media/store_icons/er_1744452012018.jpg",
            "handle": "hanuman_coders",
            "name": "Hanuman Coders",
            "reference": "S1744452089786"
        },
        {
            "post_reference": "PO202505101400400956",
            "post_id": 101,
            "quote_parent_id": null,
            "user_reference": null,
            "store_reference": "S1744452089786",
            "post_text": "Test post to check pagination 49, Jai Shree Ram. Thermodynamics is the branch of physics that studies the relationships between heat, energy, and work. It describes how energy moves and transforms in systems, particularly in terms of heat transfer and mechanical work. It is fundamental to understanding engines, refrigeration, and many natural and industrial processes.",
            "created_date": "2025-05-10 14:00:40.797332+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "post_images": [
                {
                    "post_image_id": "fd66c83f-ee23-49aa-8b97-8dfbaf0b62a7",
                    "post_image": "/media/social_post_images/wp4678876-dragon-ball-super-4k-wallpapers_vDRcOZh.jpg",
                    "created_date": "2025-05-10T14:00:41.097254+05:30",
                    "is_deleted": false,
                    "reorder": 1
                }
            ],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_header_text": "",
            "content_headers": [],
            "content_type": "POST",
            "icon": "/media/store_icons/er_1744452012018.jpg",
            "handle": "hanuman_coders",
            "name": "Hanuman Coders",
            "reference": "S1744452089786"
        }
    ]
}    
```


## Get recommended Products 

### Curl
```bash
curl --location 'https://e2e-77-175.ssdcloudindia.net/dev/lean/recommended_products/?limit=10&offset=0&visitor_reference=U1719579800140&user_pincode=524004' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>'
```

### Response
```json
{
    "message": "success",
    "data": [
        {
            "productid": 105,
            "product_reference": "P1745875065560581OMHU",
            "product_name": "planets",
            "product_description": "HD 4k quality",
            "product_category": "planets",
            "product_version": "4.0.0",
            "updated_date": "06:05:2025 16:22:35",
            "created_date": "29:04:2025 02:47:45",
            "promotion_link": "https://www.planets.com",
            "brand_name": "Kitten kreeps",
            "mrp_price": 2500,
            "selling_price": 2000,
            "count_of_ratings": 0,
            "rating": null,
            "in_stock": 100000,
            "hashtags": "#planets",
            "store_reference": "S1721930951916",
            "storeid": 10,
            "targeted_gender": "U",
            "swadeshi_brand": "FULLY_SWADESHI_BRAND",
            "swadeshi_made": "FULLY_SWADESHI_MADE",
            "deleted": false,
            "like_count": 1,
            "comment_count": 1,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "orders_count": 0,
            "returns_count": 0,
            "cancels_count": 0,
            "modified_date": "2025-05-06 16:22:35.146161+05:30",
            "promotion_amount": 0,
            "is_promotion_enabled": false,
            "analytics_view_count": 100,
            "store_icon": "store_icons/wallpaperflare.com_wallpaper (3).jpg",
            "store_name": "Kittenkreeps",
            "storehandle": "kitten_kreeps",
            "is_test_store": false,
            "subscription_type": "FREE",
            "open_for_order": true,
            "store_valuation": 0.0,
            "store_level": "1",
            "store_analytics_view_count": 0,
            "location": "Nellore",
            "swadeshi_owned": "FULLY_SWADESHI_OWNED",
            "prod_images": [
                {
                    "productimageid": 310,
                    "product_image": "post_images/STScI-01EVSVE3VNSYF8J7J7AZ4P6NB8.jpg",
                    "created_date": "2025-04-29T02:47:46.430308+05:30",
                    "is_deleted": false,
                    "reorder": 1
                },
                {
                    "productimageid": 311,
                    "product_image": "post_images/STScI-01EVST70HNTJNZ5V6SDJVKB5Q2.png",
                    "created_date": "2025-04-29T02:47:47.185823+05:30",
                    "is_deleted": false,
                    "reorder": 2
                },
                {
                    "productimageid": 312,
                    "product_image": "post_images/STScI-01EVSSW78ZSG7KP71HMSDFM5MH.png",
                    "created_date": "2025-04-29T02:47:49.631864+05:30",
                    "is_deleted": false,
                    "reorder": 3
                },
                {
                    "productimageid": 313,
                    "product_image": "post_images/STScI-01EVSSWGY8JTJ8MMBYTC0QR68F.png",
                    "created_date": "2025-04-29T02:47:53.191827+05:30",
                    "is_deleted": false,
                    "reorder": 4
                }
            ],
            "delivery_settings_type": "store_settings",
            "delivery_by": 4,
            "delivery_partner": "self",
            "delivery_fee": "free",
            "logistic_partner_name": null,
            "fulfillment_options": "IN_STORE_PICKUP",
            "return_settings_type": "store_settings",
            "return_period": 7,
            "return_conditions": [
                "No damage"
            ],
            "return_pick_up": "seller",
            "return_cost_on": "SELLER",
            "refund_responsibility": [
                {
                    "item_text": "Full refund based on conditions",
                    "item_heading": "Refund policy",
                    "item_subtext": ""
                },
                {
                    "item_text": "Full Refund",
                    "item_heading": "If the seller cancels or if the order is auto-canceled",
                    "item_subtext": ""
                },
                {
                    "item_text": "Partial Refund",
                    "item_heading": "If the customer cancels before order confirmation",
                    "item_subtext": "Transaction fee charged by payment gateway (usually 2% + 18% GST if any) will be reduced from the refund amount"
                },
                {
                    "item_text": "Full Refund",
                    "item_heading": "On product returns",
                    "item_subtext": ""
                }
            ],
            "save_status": false,
            "repost_status": false,
            "like_status": true,
            "content_category": "POST",
            "content_header_text": "",
            "content_headers": [],
            "content_type": "PRODUCT",
            "icon": "/media/store_icons/wallpaperflare.com_wallpaper%20(3).jpg",
            "handle": "kitten_kreeps",
            "name": "Kittenkreeps",
            "reference": "S1721930951916",
            "deliverability": false,
            "is_buy_enabled": false,
            "product_status_message": "Not deliverable to your pincode",
            "config_receive_orders": true,
            "disclaimer_message": "Shop with care|This new store is not yet public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual."
        },
        {
            "productid": 104,
            "product_reference": "P1745870510016538IKHQ",
            "product_name": "Eyes pics",
            "product_description": "Anime eyes",
            "product_category": "Ai Images",
            "product_version": "5.0.0",
            "updated_date": "29:04:2025 02:48:27",
            "created_date": "29:04:2025 01:31:50",
            "promotion_link": "https://www.animeeyes.com",
            "brand_name": "Kitten kreeps",
            "mrp_price": 2500,
            "selling_price": 2000,
            "count_of_ratings": 0,
            "rating": null,
            "in_stock": 5000,
            "hashtags": "#anime",
            "store_reference": "S1721930951916",
            "storeid": 10,
            "targeted_gender": "U",
            "swadeshi_brand": "FULLY_SWADESHI_BRAND",
            "swadeshi_made": "FULLY_SWADESHI_MADE",
            "deleted": false,
            "like_count": 0,
            "comment_count": 8,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "orders_count": 0,
            "returns_count": 0,
            "cancels_count": 0,
            "modified_date": "2025-04-29 02:48:27.568522+05:30",
            "promotion_amount": 0,
            "is_promotion_enabled": false,
            "analytics_view_count": 100,
            "store_icon": "store_icons/wallpaperflare.com_wallpaper (3).jpg",
            "store_name": "Kittenkreeps",
            "storehandle": "kitten_kreeps",
            "is_test_store": false,
            "subscription_type": "FREE",
            "open_for_order": true,
            "store_valuation": 0.0,
            "store_level": "1",
            "store_analytics_view_count": 0,
            "location": "Nellore",
            "swadeshi_owned": "FULLY_SWADESHI_OWNED",
            "prod_images": [
                {
                    "productimageid": 305,
                    "product_image": "post_images/1745870417894.jpg",
                    "created_date": "2025-04-29T01:31:50.793516+05:30",
                    "is_deleted": false,
                    "reorder": 1
                },
                {
                    "productimageid": 306,
                    "product_image": "post_images/1745870417894_dAM4vHB.jpg",
                    "created_date": "2025-04-29T01:31:51.33545+05:30",
                    "is_deleted": false,
                    "reorder": 2
                },
                {
                    "productimageid": 307,
                    "product_image": "post_images/1745870417894_ERi5vY0.jpg",
                    "created_date": "2025-04-29T01:31:51.887396+05:30",
                    "is_deleted": false,
                    "reorder": 3
                },
                {
                    "productimageid": 308,
                    "product_image": "post_images/1745870417894_01ftVoi.jpg",
                    "created_date": "2025-04-29T01:31:52.307558+05:30",
                    "is_deleted": false,
                    "reorder": 4
                },
                {
                    "productimageid": 309,
                    "product_image": "post_images/1745870417894_OGb9eOE.jpg",
                    "created_date": "2025-04-29T01:31:52.731248+05:30",
                    "is_deleted": false,
                    "reorder": 5
                }
            ],
            "delivery_settings_type": "store_settings",
            "delivery_by": 4,
            "delivery_partner": "self",
            "delivery_fee": "free",
            "logistic_partner_name": null,
            "fulfillment_options": "IN_STORE_PICKUP",
            "return_settings_type": "store_settings",
            "return_period": 7,
            "return_conditions": [
                "No damage"
            ],
            "return_pick_up": "seller",
            "return_cost_on": "SELLER",
            "refund_responsibility": [
                {
                    "item_text": "Full refund based on conditions",
                    "item_heading": "Refund policy",
                    "item_subtext": ""
                },
                {
                    "item_text": "Full Refund",
                    "item_heading": "If the seller cancels or if the order is auto-canceled",
                    "item_subtext": ""
                },
                {
                    "item_text": "Partial Refund",
                    "item_heading": "If the customer cancels before order confirmation",
                    "item_subtext": "Transaction fee charged by payment gateway (usually 2% + 18% GST if any) will be reduced from the refund amount"
                },
                {
                    "item_text": "Full Refund",
                    "item_heading": "On product returns",
                    "item_subtext": ""
                }
            ],
            "save_status": false,
            "repost_status": false,
            "like_status": false,
            "content_category": "POST",
            "content_header_text": "",
            "content_headers": [],
            "content_type": "PRODUCT",
            "icon": "/media/store_icons/wallpaperflare.com_wallpaper%20(3).jpg",
            "handle": "kitten_kreeps",
            "name": "Kittenkreeps",
            "reference": "S1721930951916",
            "deliverability": false,
            "is_buy_enabled": false,
            "product_status_message": "Not deliverable to your pincode",
            "config_receive_orders": true,
            "disclaimer_message": "Shop with care|This new store is not yet public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual."
        }
    ]
}
```

## Get tagged posts to a content
### Curl
```bash
curl --location 'http://192.168.1.6:8000/lean/get_tagged_post_details/?limit=100&offset=0&visitor_reference=U1744450273600&content_reference=P1750677110400549UCDQ' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>'
```

### Response
```json
{
    "message": "success",
    "data": [
        {
            "post_reference": "PO202505101400414809",
            "post_id": 102,
            "quote_parent_id": null,
            "user_reference": null,
            "store_reference": "S1744452089786",
            "post_text": "Test post to check pagination 50, Jai Shree Ram. Thermodynamics is the branch of physics that studies the relationships between heat, energy, and work. It describes how energy moves and transforms in systems, particularly in terms of heat transfer and mechanical work. It is fundamental to understanding engines, refrigeration, and many natural and industrial processes.",
            "created_date": "2025-05-10 14:00:41.513888+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "post_images": [
                {
                    "post_image_id": "edbb0f8f-5ccb-4f9b-847c-2250c89af967",
                    "post_image": "/media/social_post_images/wp4678876-dragon-ball-super-4k-wallpapers_L5WkTUG.jpg",
                    "created_date": "2025-05-10T14:00:41.85204+05:30",
                    "is_deleted": false,
                    "reorder": 1
                }
            ],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_header_text": "",
            "content_headers": [],
            "content_type": "POST",
            "icon": "/media/store_icons/er_1744452012018.jpg",
            "handle": "hanuman_coders",
            "name": "Hanuman Coders",
            "reference": "S1744452089786"
        },
        {
            "post_reference": "PO202505101400400956",
            "post_id": 101,
            "quote_parent_id": null,
            "user_reference": null,
            "store_reference": "S1744452089786",
            "post_text": "Test post to check pagination 49, Jai Shree Ram. Thermodynamics is the branch of physics that studies the relationships between heat, energy, and work. It describes how energy moves and transforms in systems, particularly in terms of heat transfer and mechanical work. It is fundamental to understanding engines, refrigeration, and many natural and industrial processes.",
            "created_date": "2025-05-10 14:00:40.797332+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "post_images": [
                {
                    "post_image_id": "fd66c83f-ee23-49aa-8b97-8dfbaf0b62a7",
                    "post_image": "/media/social_post_images/wp4678876-dragon-ball-super-4k-wallpapers_vDRcOZh.jpg",
                    "created_date": "2025-05-10T14:00:41.097254+05:30",
                    "is_deleted": false,
                    "reorder": 1
                }
            ],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_header_text": "",
            "content_headers": [],
            "content_type": "POST",
            "icon": "/media/store_icons/er_1744452012018.jpg",
            "handle": "hanuman_coders",
            "name": "Hanuman Coders",
            "reference": "S1744452089786"
        }
    ]
}