import 'package:flutter/material.dart';
import 'package:swadesic/model/product_option/product_option.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';

class AddOptionBottomSheet extends StatefulWidget {
  final ProductOption? existingOption;
  final Function(ProductOption) onOptionAdded;
  final VoidCallback? onOptionDeleted;

  const AddOptionBottomSheet({
    super.key,
    this.existingOption,
    required this.onOptionAdded,
    this.onOptionDeleted,
  });

  @override
  State<AddOptionBottomSheet> createState() => _AddOptionBottomSheetState();
}

class _AddOptionBottomSheetState extends State<AddOptionBottomSheet> {
  late TextEditingController optionNameController;
  late TextEditingController optionValueController;
  List<String> optionValues = [];
  bool isEditing = false;

  @override
  void initState() {
    super.initState();
    isEditing = widget.existingOption != null;
    
    optionNameController = TextEditingController(
      text: widget.existingOption?.optionName ?? '',
    );
    optionValueController = TextEditingController();
    
    if (widget.existingOption != null) {
      optionValues = List<String>.from(widget.existingOption!.optionValues);
    }
  }

  @override
  void dispose() {
    optionNameController.dispose();
    optionValueController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          left: 20,
          right: 20,
          top: 20,
          bottom: MediaQuery.of(context).viewInsets.bottom + 20,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Center(
              child: Text(
                isEditing ? AppStrings.editOption : AppStrings.addAnOption,
                style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
              ),
            ),
            verticalSizedBox(30),
            
            // Option Name
            Text(
              AppStrings.optionName,
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
            verticalSizedBox(10),
            AppTextFields.allTextField(
              context: context,
              textEditingController: optionNameController,
              hintText: "Option name",
            ),
            verticalSizedBox(20),
            
            // Option Values
            Text(
              AppStrings.optionValues,
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
            verticalSizedBox(10),
            
            // Add value input
            Row(
              children: [
                Expanded(
                  child: AppTextFields.allTextField(
                    context: context,
                    textEditingController: optionValueController,
                    hintText: AppStrings.enterOptionValue,
                    onSaved: (value) {
                      addOptionValue();
                    },
                  ),
                ),
                horizontalSizedBox(10),
                GestureDetector(
                  onTap: addOptionValue,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: const BoxDecoration(
                      color: AppColors.appBlack,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.add,
                      color: AppColors.appWhite,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
            verticalSizedBox(15),
            
            // Option values chips
            if (optionValues.isNotEmpty) ...[
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: optionValues.map((value) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppColors.textFieldFill1,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          value,
                          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
                        ),
                        horizontalSizedBox(8),
                        GestureDetector(
                          onTap: () => removeOptionValue(value),
                          child: const Icon(
                            Icons.close,
                            size: 16,
                            color: AppColors.appBlack,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
              verticalSizedBox(30),
            ],
            
            // Action buttons
            if (isEditing) ...[
              // Update and Delete buttons for editing
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        if (widget.onOptionDeleted != null) {
                          widget.onOptionDeleted!();
                          Navigator.pop(context);
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.red),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Text(
                            AppStrings.delete,
                            style: AppTextStyle.contentText0(textColor: Colors.red),
                          ),
                        ),
                      ),
                    ),
                  ),
                  horizontalSizedBox(15),
                  Expanded(
                    flex: 2,
                    child: GestureDetector(
                      onTap: updateOption,
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        decoration: BoxDecoration(
                          color: AppColors.appBlack,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Text(
                            AppStrings.update,
                            style: AppTextStyle.contentText0(textColor: AppColors.appWhite),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ] else ...[
              // Add button for new option
              GestureDetector(
                onTap: addOption,
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  decoration: BoxDecoration(
                    color: AppColors.appBlack,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      AppStrings.add,
                      style: AppTextStyle.contentText0(textColor: AppColors.appWhite),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void addOptionValue() {
    final value = optionValueController.text.trim();
    if (value.isNotEmpty && !optionValues.contains(value)) {
      setState(() {
        optionValues.add(value);
        optionValueController.clear();
      });
    }
  }

  void removeOptionValue(String value) {
    setState(() {
      optionValues.remove(value);
    });
  }

  void addOption() {
    final optionName = optionNameController.text.trim();
    
    if (optionName.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppStrings.pleaseEnterOptionName)),
      );
      return;
    }
    
    if (optionValues.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppStrings.pleaseAddAtLeastOneOptionValue)),
      );
      return;
    }

    final option = ProductOption(
      optionName: optionName,
      optionValues: optionValues,
    );

    widget.onOptionAdded(option);
    Navigator.pop(context);
  }

  void updateOption() {
    final optionName = optionNameController.text.trim();
    
    if (optionName.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please enter option name")),
      );
      return;
    }
    
    if (optionValues.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please add at least one option value")),
      );
      return;
    }

    final option = ProductOption(
      optionName: optionName,
      optionValues: optionValues,
    );

    widget.onOptionAdded(option);
    Navigator.pop(context);
  }
}
