import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/product_labels_detail/product_labels_detail.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/common_methods.dart';

class ProductLabelDropdownBloc{
  //region Common variable
  late BuildContext context;
  bool isDropDownVisible = false;
  Product product  = Product();

  //endregion

//region Text Editing Controller
//endregion

//region Controller
  final refreshLabelCtrl = StreamController<bool>.broadcast();
//endregion
  //region Constructor
  ProductLabelDropdownBloc(this.context, this.product);
  //endregion
//region Init
  void init(){

  }
//endregion

  //region On tap dropdown
  void onTapDropDown(){
    isDropDownVisible = !isDropDownVisible;
    //refresh
    refreshLabelCtrl.sink.add(true);
  }
  //endregion

  //region On tap labels
  Future<void> onTapLabels({required String bottomSheetName,
     bool isMade = false,
     bool isBrand= false,
    bool isOwned= false,

  }){
    return CommonMethods.appBottomSheet(screen:ProductLabelsDetail(isMade: isMade,
    isBrand:isBrand ,
      isOwned:isOwned, currentFlag:bottomSheetName,
    ),
        context: context,
        bottomSheetName:bottomSheetName);

  }
  //endregion


//region Dispose
void dispose(){
  refreshLabelCtrl.close();
}
//endregion
}