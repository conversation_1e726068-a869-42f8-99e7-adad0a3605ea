import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';

class StoreProductResponse {
  String? message;
  List<Product>? data;

  StoreProductResponse({this.message, this.data});

  StoreProductResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['data'] != null) {
      data = <Product>[];
      json['data'].forEach((v) {
        data!.add(Product.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Product {
  int? productid;
  bool isPinCodeChanged = false;
  String? contentType;
  String? productReference;
  String? createdDate;
  String? subscriptionType;
  String? storeIcon;
  String? productName;
  String? productDescription;
  String? promotionLink;
  String? storehandle;
  String? storeName;
  String? deliveryFee;
  String? productVersion;
  String? updatedDate;
  String? returnPickupBy;
  bool? deliverable;
  String? brandName;
  int? mrpPrice;
  int? sellingPrice;
  int? ordersCount;
  int? returnCount;
  String? location;
  int? countOfRatings;
  double? rating;
  List<ProdImages>? prodImages;
  List<TaggedStory>? taggedStories;
  int? commentCount;
  int? storeid;
  int? inStock;
  String? hashTag;
  String? productCategory;
  String? storeReference;
  int? returnPeriod;
  int? deliveryBy;
  List<String>? returnConditions;
  String? returnCostOn;
  String? deliveryPartner;
  String? logisticPartnerName;
  String? swadeshiOwned;
  String? swadeshiMade;
  String? swadeshiBrand;
  String? targetGender;
  bool? isDeleted;
  bool? configReceiveOrders;
  bool? openForOrder;
  bool? isTestStore;
  int? likeCount;
  bool? likeStatus;
  bool? saveStatus = false;
  List<RefundResponsibility>? refundResponsibilityList;
  bool? isBuyEnable;
  String? productStatusMessage;
  String? contentCategory;
  String? contentHeaderText;
  String? fulfillmentOptions;
  bool? isPromotionEnabled;
  double? promotionAmount;
  String? disclaimerMessage;
  int? analyticsViewCount;
  String? level;
  String? productSlug;
  String? productCode;
  Map<String, List<String>>? options;
  List<Map<String, dynamic>>? variants; // Store variants as JSON for cache
  ProductVariant? variantDetails; // Specific variant details from order context
  int? totalTaggedPosts;
  List<Map<String, dynamic>>? taggedPostImages;

  Product(
      {this.productid,
      this.storeIcon,
      this.openForOrder,
      this.contentType,
      this.subscriptionType,
      this.productReference,
      this.productName,
      this.storeName,
      this.createdDate,
      this.productDescription,
      this.deliveryFee,
      this.deliverable,
      this.contentCategory,
      this.contentHeaderText,
      this.ordersCount,
      this.returnCount,
      this.promotionLink,
      this.returnPickupBy,
      this.storehandle,
      this.productVersion,
      this.updatedDate,
      this.saveStatus,
      this.brandName,
      this.mrpPrice,
      this.targetGender,
      this.sellingPrice,
      this.location,
      this.countOfRatings,
      this.rating,
      this.prodImages,
      this.commentCount,
      this.storeid,
      this.inStock,
      this.productCategory,
      this.storeReference,
      this.returnPeriod,
      this.deliveryBy,
      this.returnConditions,
      this.hashTag,
      this.returnCostOn,
      this.deliveryPartner,
      this.logisticPartnerName,
      this.swadeshiOwned,
      this.swadeshiBrand,
      this.swadeshiMade,
      this.isDeleted,
      this.configReceiveOrders,
      this.isTestStore,
      this.likeStatus,
      this.likeCount,
      this.refundResponsibilityList,
      this.isBuyEnable,
      this.productStatusMessage,
      this.fulfillmentOptions,
      this.isPromotionEnabled,
      this.promotionAmount,
      this.disclaimerMessage,
      this.analyticsViewCount,
      this.level,
      this.productSlug,
      this.productCode,
      this.options,
      this.variants,
      this.variantDetails,
      this.totalTaggedPosts,
      this.taggedPostImages,
      });

  Product.fromJson(Map<String, dynamic> json) {
    //print("Product.fromJson - Raw JSON for fulfillmentOptions: ${json['fulfillmentOptions']}");
    //print("Product.fromJson - Available keys: ${json.keys.toList()}");
    //print("Product.fromJson - Raw JSON: $json");

    //If product detail contains "contentType" == "PRODUCT"
    if (json['contentType'] != null && json['contentType'] == "PRODUCT") {
      contentType = json['contentType'];
      likeCount = json['likeCount'];
      commentCount = json['commentCount'];
      subscriptionType = json['createdBy']['subscriptionType'] ?? "";
      createdDate = json['createdBy']['createdDate'];
      likeStatus = json['likeStatus'] ?? false;
      saveStatus = json['saveStatus'];
      productReference = json['productReference'];
      productName = json['productName'];
      brandName = json['brandName'];
      productCategory = json['productCategory'];
      storeReference = json['storeReference'];
      productDescription = json['productDescription'];
      promotionLink = json['promotionLink'];
      hashTag = json['hashtags'];
      inStock = json['inStock'];
      mrpPrice = json['mrpPrice'];
      sellingPrice = json['sellingPrice'];
      isDeleted = json['isDeleted'];
      contentCategory = json['contentCategory'];
      contentHeaderText = json['contentHeaderText'];
      swadeshiMade = json['swadeshiMade'];
      swadeshiBrand = json['swadeshiBrand'];
      ordersCount = json['ordersCount'];
      returnCount = json['returnsCount'];
      productVersion = json['productVersion'];
      updatedDate = json['modifiedDate'];

      //Product image from store and single product
      if (json['productImages'] != null) {
        prodImages = <ProdImages>[];
        json['productImages'].forEach((v) {
          prodImages!.add(ProdImages.fromJson(v));
        });
      }
      productid = json['productid'];
      swadeshiOwned = json['swadeshiOwned'];
      storeReference = json['createdBy']['reference'];
      storehandle = json['createdBy']['handle'];
      storeName = json['createdBy']['name'];
      storeIcon = json['createdBy']['icon'];
      configReceiveOrders = json['configReceiveOrders'];
      returnPeriod =
          json['returnPeriod'] != null ? int.parse(json['returnPeriod']) : 0;
      returnCostOn = json['returnCostOn'];
      returnConditions = json['returnConditions'].cast<String>();
      returnPickupBy = json['returnPickUp'];
      deliveryBy =
          json['deliveryBy'] != null ? int.parse(json['deliveryBy']) : 0;
      deliveryPartner = json['deliveryPartner'];
      deliveryFee = json['deliveryFee'];
      logisticPartnerName = json['logisticPartnerName'];
      deliverable = json['deliverability'];
      storeid = json['storeId'];
      //Refund responsibility list
      if (json['refundResponsibility'] != null) {
        refundResponsibilityList = <RefundResponsibility>[];
        json['refundResponsibility'].forEach((v) {
          refundResponsibilityList!.add(RefundResponsibility.fromJson(v));
        });
      }
      if (CommonMethods().isStaticUser()) {
        isBuyEnable = true;
      } else {
        isBuyEnable = json['isBuyEnabled'] ?? false;
      }
      productStatusMessage = json['productStatusMessage'] ?? "";
      fulfillmentOptions = json['fulfillmentOptions'];
      //print("Product.fromJson - Set fulfillmentOptions to: $fulfillmentOptions");
      isPromotionEnabled = json['isAffiliatePromotionEnabled'] ?? false;
      promotionAmount = json['affiliateCommissionAmount']?.toDouble();
      disclaimerMessage = json['disclaimerMessage'];
      analyticsViewCount = json['analyticsViewCount'] ?? 0;
      level = json['level'] ?? "1";
      productSlug = json['productSlug'] ?? "";
      productCode = json['productCode'] ?? "";
      totalTaggedPosts = json['totalTaggedPosts'];
      taggedPostImages = json['taggedPostImages'];

      //Tagged stories
      // if (json['tagged_stories'] != null) {
      if (json['tagged_stories'] == null) {
        taggedStories = <TaggedStory>[
          TaggedStory(
              title:
                  "Swadeshi: A Journey of Pride and Patriotism with Swadeshi Products",
              icon: "/media/post_images/1729338144569.jpg",
              storyReference: "SJGhgh777"),
          TaggedStory(
              title:
                  "हरे कृष्णा हरे कृष्णा कृष्णा कृष्णा हरे हरे,हरे राम हरे राम राम राम हरे हरे",
              icon: "/media/post_images/1729338144569.jpg",
              storyReference: "SJGhgh777"),
          TaggedStory(
              title: "Hare rama",
              icon: "/media/post_images/1729338144569.jpg",
              storyReference: "SJGhgh777"),
        ];
        // json['tagged_stories'].forEach((v) {
        //   taggedStories!.add(TaggedStory.fromJson(v));
        // });
      }
    }

    //If not from feed/whats on swadesic
    else {
      productid = json['productid'];
      storeIcon = json['store_icon'] ??
          (json['createdBy'] != null ? json['createdBy']['icon'] : null);
      contentType = json['content_type'];
      subscriptionType =
          json['subscriptionType'] ?? json['subscription_type'] ?? "";
      productReference = json['product_reference'] ?? json['productReference'];
      productName = json['product_name'] ?? json['productName'];
      storeName = json['store_name'];
      contentCategory = json['content_category'];
      contentHeaderText = json['content_header_text'];
      createdDate = json['created_date'] ?? json['createdDate'];
      productDescription =
          json['product_description'] ?? json['productDescription'];
      promotionLink = json['promotion_link'] ?? json['promotionLink'];
      storehandle = json['storehandle'] ??
          (json['createdBy'] != null ? json['createdBy']['handle'] : null);
      ;
      deliveryFee = json['delivery_fee'];
      ordersCount = json['sold_count'] ??
          json['ordersCount'] ??
          json['orders_count'] ??
          0;
      returnCount = json['return_count'] ?? json['returnsCount'] ?? 0;
      targetGender = json['targeted_gender'] ?? "M";
      deliverable = json['deliverability'];
      productVersion = json['product_version'];
      updatedDate = json['updated_date'];
      openForOrder = json['open_for_order'];
      returnPickupBy = json['return_pick_up'];
      saveStatus = json['saved_or_not'] ??
          json['saveStatus'] ??
          json['save_status'] ??
          false;
      brandName = json['brand_name'] ?? json['brandName'];
      mrpPrice = json['mrp_price'] ?? json['mrpPrice'];
      sellingPrice = json['selling_price'] ?? json['sellingPrice'];
      location = json['location'] ?? "";
      configReceiveOrders = json['config_receive_orders'] ?? false;
      countOfRatings = json['count_of_ratings'];
      rating = json['rating'] ?? 0.0;
      likeCount = json['likeCount'] ?? json['like_count'] ?? 0;
      // deliveryBy = int.parse(json['delivery_by']??json['deliveryBy']);
      if (json['deliveryBy'] != null) {
        deliveryBy = int.parse(json['deliveryBy']);
      } else {
        deliveryBy = json['delivery_by'];
      }

      swadeshiOwned = json['swadeshi_owned'] ?? json['swadeshiOwned'];
      swadeshiMade = json['swadeshi_made'] ?? json['swadeshiMade'];
      swadeshiBrand = json['swadeshi_brand'] ?? json['swadeshiBrand'];
      likeStatus = json['likeStatus'] ?? json['like_status'] ?? false;

      returnCostOn = json['return_cost_on'];
      hashTag = json['hashtags'];
      deliveryPartner = json['delivery_partner'];
      logisticPartnerName = json['logistic_partner_name'];

      storeReference = json['store_reference'] ?? json['storeReference'];
      returnConditions = json['return_conditions'] == null
          ? []
          : json['return_conditions'].cast<String>();
      //Product image from store and single product
      if (json['prod_images'] != null) {
        prodImages = <ProdImages>[];
        json['prod_images'].forEach((v) {
          prodImages!.add(ProdImages.fromJson(v));
        });
      }
      //Product from feed
      if (json['productImages'] != null) {
        prodImages = <ProdImages>[];
        json['productImages'].forEach((v) {
          //print("Media path is ${json['mediaPath']}");
          prodImages!.add(ProdImages.fromJson(v));

          // prodImages!.add(ProdImages(productImage:json['mediaPath'],reorder:json['order'],productimageid: json['mediaId']));
        });
      }

      //Tagged stories
      // if (json['tagged_stories'] != null) {
      if (json['tagged_stories'] == null) {
        taggedStories = <TaggedStory>[
          TaggedStory(
              title:
                  "Swadeshi: A Journey of Pride and Patriotism with Swadeshi Products",
              icon: "/media/post_images/1729338144569.jpg",
              storyReference: "SJGhgh777"),
          TaggedStory(
              title:
                  "हरे कृष्णा हरे कृष्णा कृष्णा कृष्णा हरे हरे,हरे राम हरे राम राम राम हरे हरे",
              icon: "/media/post_images/1729338144569.jpg",
              storyReference: "SJGhgh777"),
          TaggedStory(
              title: "Hare rama",
              icon: "/media/post_images/1729338144569.jpg",
              storyReference: "SJGhgh777"),
        ];
        // json['tagged_stories'].forEach((v) {
        //   taggedStories!.add(TaggedStory.fromJson(v));
        // });
      }
      commentCount = json['comment_count'] ?? json['commentCount'] ?? 78;
      storeid = json['storeid'];
      isDeleted = json['deleted'];
      inStock = json['in_stock'] ?? json['inStock'];
      returnPeriod = json['return_period'] ?? 0;
      isTestStore = json['is_test_store'] ?? false;
      productCategory = json['product_category'] ?? "null";
      //Refund responsibility list
      if (json['refund_responsibility'] != null) {
        refundResponsibilityList = <RefundResponsibility>[];
        json['refund_responsibility'].forEach((v) {
          refundResponsibilityList!.add(RefundResponsibility.fromJson(v));
        });
      }

      if (CommonMethods().isStaticUser()) {
        isBuyEnable = true;
      } else {
        isBuyEnable = json['is_buy_enabled'] ?? false;
      }

      productStatusMessage = json['product_status_message'] ?? "";

      fulfillmentOptions =
          json['fulfillment_options']; // Added mapping for fulfillmentOptions
      isPromotionEnabled = json['is_promotion_enabled'] ?? false;
      promotionAmount = json['promotion_amount']?.toDouble();
      disclaimerMessage = json['disclaimer_message'] ?? "";
      analyticsViewCount = json['analytics_view_count'] ?? 0;
      level = json['level'] ?? "1";
      productSlug = json['product_slug'] ?? "";
      productCode = json['product_code'] ?? "";
      options= json['options'] != null
          ? Map<String, List<String>>.from(
              json['options'].map((key, value) => MapEntry(
                key.toString(),
                List<String>.from(value)
              ))
            )
          : null;
      variants= json['product_variants'] != null
          ? List<Map<String, dynamic>>.from(json['product_variants'])
          : null;

      // Parse variant_details from order context
      variantDetails = json['variant_details'] != null
          ? ProductVariant.fromJson(json['variant_details'])
          : null;
      totalTaggedPosts = json['total_tagged_posts'];
      taggedPostImages = json['tagged_post_images'] != null
          ? List<Map<String, dynamic>>.from(json['tagged_post_images'])
          : null;
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['contentType'] = contentType;
    data['productid'] = productid;
    data['store_icon'] = storeIcon;
    data['product_reference'] = productReference;
    data['product_name'] = productName;
    data['product_description'] = productDescription;
    data['store_name'] = storeName;
    data['created_date'] = createdDate;
    data['return_count'] = returnCount;
    data['sold_count'] = ordersCount;
    data['open_for_order'] = openForOrder;
    data['promotion_link'] = promotionLink;
    data['deliverability'] = deliverable;
    data['config_receive_orders'] = configReceiveOrders;
    data['storehandle'] = storehandle;
    data['likeStatus'] = likeStatus;
    data['product_version'] = productVersion;
    data['targeted_gender'] = targetGender;
    data['updated_date'] = updatedDate;
    data['likeCount'] = likeCount;
    data['delivery_fee'] = deliveryFee;
    data['return_pick_up'] = returnPickupBy;
    data['saved_or_not'] = saveStatus;
    data['brand_name'] = brandName;
    data['mrp_price'] = mrpPrice;
    data['selling_price'] = sellingPrice;
    data['location'] = location;
    data['hashtags'] = hashTag;
    data['deleted'] = isDeleted;
    data['is_test_store'] = isTestStore;
    data['return_cost_on'] = returnCostOn;
    data['delivery_partner'] = deliveryPartner;
    data['logistic_partner_name'] = logisticPartnerName;

    data['count_of_ratings'] = countOfRatings;
    data['rating'] = rating;
    data['delivery_by'] = deliveryBy;
    data['return_conditions'] = returnConditions;
    data['store_reference'] = storeReference;
    if (prodImages != null) {
      data['prod_images'] = prodImages!.map((v) => v.toJson()).toList();
    }
    data['comment_count'] = commentCount;
    data['storeid'] = storeid;
    data['in_stock'] = inStock;
    data['product_category'] = productCategory;
    data['return_period'] = returnPeriod;
    data['swadeshi_owned'] = swadeshiOwned;

    data['swadeshi_made'] = swadeshiMade;
    data['swadeshi_brand'] = swadeshiBrand;
    data['fulfillment_options'] = fulfillmentOptions;
    //print("Product.toJson - Serializing fulfillmentOptions: $fulfillmentOptions");
    data['is_promotion_enabled'] = isPromotionEnabled;
    data['promotion_amount'] = promotionAmount;
    data['disclaimer_message'] = disclaimerMessage;
    data['analytics_view_count'] = analyticsViewCount;
    data['level'] = level;
    data['product_slug'] = productSlug;
    data['product_code'] = productCode;
    data['options'] = options;
    data['variants'] = variants;
    if (variantDetails != null) {
      data['variant_details'] = variantDetails!.toJson();
    }
    data['total_tagged_posts'] = totalTaggedPosts;
    data['tagged_post_images'] = taggedPostImages;
    return data;
  }

  // Static method to create a Product from lean API response
  static Product fromLeanJson(Map<String, dynamic> json) {
    // Create product images list if available
    List<ProdImages>? prodImages;

    // Handle product_images from lean API
    if (json['product_images'] != null) {
      prodImages = <ProdImages>[];
      json['product_images'].forEach((v) {
        prodImages!.add(ProdImages.fromJson(v));
      });
    }
    // Handle productImages from feed API
    else if (json['productImages'] != null) {
      prodImages = <ProdImages>[];
      json['productImages'].forEach((v) {
        prodImages!.add(ProdImages.fromJson(v));
      });
    }
    // Handle prod_images from other APIs
    else if (json['prod_images'] != null) {
      prodImages = <ProdImages>[];
      json['prod_images'].forEach((v) {
        prodImages!.add(ProdImages.fromJson(v));
      });
    }

    // Handle store reference which might come in different formats
    String? storeRef = json['store_reference'] ?? json['reference'];
    String? storeHandle = json['storehandle'] ?? json['handle'];
    String? storeIconPath = json['store_icon'] ?? json['icon'];
    String? storeName = json['store_name'] ?? json['name'];
    String? level = json['store_level'] ?? "1";

    return Product(
      contentType: 'PRODUCT',
      productReference: json['product_reference'],
      productName: json['product_name'],
      brandName: json['brand_name'],
      productCategory: json['product_category'],
      productDescription: json['product_description'],
      mrpPrice: json['mrp_price'] is String && json['mrp_price'] != null
          ? int.tryParse(json['mrp_price'])
          : json['mrp_price'],
      sellingPrice:
          json['selling_price'] is String && json['selling_price'] != null
              ? int.tryParse(json['selling_price'])
              : json['selling_price'],
      prodImages: prodImages,
      storeReference: storeRef,
      storeIcon: storeIconPath,
      storehandle: storeHandle,
      storeName: storeName,
      likeCount: json['like_count'] ?? 0,
      commentCount: json['comment_count'] ?? 0,
      likeStatus: json['like_status'] ?? false,
      saveStatus: json['save_status'] ?? false,
      createdDate: json['created_date'] ?? DateTime.now().toString(),
      inStock: json['in_stock'] ?? 0,
      swadeshiBrand: json['swadeshi_brand'] ?? "FULLY_SWADESHI_BRAND",
      swadeshiMade: json['swadeshi_made'] ?? "FULLY_SWADESHI_MADE",
      swadeshiOwned: json['swadeshi_owned'] ?? "FULLY_SWADESHI_OWNED",
      isDeleted: json['is_deleted'] ?? false,
      isBuyEnable: json['is_buy_enabled'] ?? true,
      productStatusMessage: json['product_status_message'] ?? "",
      contentCategory: json['content_category'] ?? "POST",
      contentHeaderText: json['content_header_text'] ?? "",
      // Add these fields to fix the null check operator error
      ordersCount: json['sold_count'] ??
          json['ordersCount'] ??
          json['orders_count'] ??
          0,
      returnCount: json['return_count'] ?? json['returnsCount'] ?? 0,
      // Additional fields that might be used in the UI
      fulfillmentOptions: json['fulfillment_options'] ?? "DELIVERY",
      targetGender: json['targeted_gender'] ?? "U",
      deliverable: json['deliverability'] ?? true,
      disclaimerMessage: json['disclaimer_message'] ?? "",
      hashTag: json['hashtags'] ?? "",
      productVersion: json['product_version'] ?? "1.0.0",
      promotionLink: json['promotion_link'] ?? "",
      rating: json['rating'] == null
          ? 0.0
          : json['rating'] is int
              ? (json['rating'] as int).toDouble()
              : json['rating'] is String
                  ? double.tryParse(json['rating']) ?? 0.0
                  : json['rating'] is double
                      ? json['rating']
                      : 0.0,
      countOfRatings: json['count_of_ratings'] ?? 0,
      location: json['location'] ?? "",
      configReceiveOrders: json['config_receive_orders'] ?? true,
      productid: json['productid'] is String && json['productid'] != null
          ? int.tryParse(json['productid'])
          : json['productid'],
      subscriptionType: json['subscription_type'] ?? "",
      updatedDate: json['updated_date'] ?? "",
      returnPickupBy: json['return_pickup_by'] ?? "",
      deliveryFee: json['delivery_fee'] ?? "",
      storeid: json['storeid'] is String && json['storeid'] != null
          ? int.tryParse(json['storeid'])
          : json['storeid'],
      returnPeriod:
          json['return_period'] is String && json['return_period'] != null
              ? int.tryParse(json['return_period'])
              : json['return_period'],
      deliveryBy: json['delivery_by'] is String && json['delivery_by'] != null
          ? int.tryParse(json['delivery_by'])
          : json['delivery_by'],
      returnConditions: json['return_conditions'] != null
          ? List<String>.from(json['return_conditions'])
          : [],
      returnCostOn: json['return_cost_on'],
      deliveryPartner: json['delivery_partner'],
      logisticPartnerName: json['logistic_partner_name'],
      isTestStore: json['is_test_store'],
      isPromotionEnabled: json['is_promotion_enabled'] ?? false,
      promotionAmount: json['promotion_amount']?.toDouble(),
      openForOrder: json['open_for_order'] ?? true,
      refundResponsibilityList: json['refund_responsibility'] != null
          ? List<RefundResponsibility>.from(json['refund_responsibility']
              .map((x) => RefundResponsibility.fromJson(x)))
          : [],
      analyticsViewCount: json['analytics_view_count'] ?? 0,
      level: level ?? "1",
      productSlug: json['product_slug'] ?? "",
      productCode: json['product_code'] ?? "",
      options: json['options'] != null
          ? Map<String, List<String>>.from(
              json['options'].map((key, value) => MapEntry(
                key.toString(),
                List<String>.from(value)
              ))
            )
          : null,
      variants: json['product_variants'] != null
          ? List<Map<String, dynamic>>.from(json['product_variants'])
          : null,
      totalTaggedPosts: json['total_tagged_posts'],
      taggedPostImages: json['tagged_post_images'] != null
          ? List<Map<String, dynamic>>.from(json['tagged_post_images'])
          : null,
    );
  }

  // Method to copy data from another Product object
  void copyFrom(Product other) {
    productid = other.productid;
    contentType = other.contentType;
    productReference = other.productReference;
    createdDate = other.createdDate;
    storeIcon = other.storeIcon;
    productName = other.productName;
    productDescription = other.productDescription;
    promotionLink = other.promotionLink;
    storehandle = other.storehandle;
    storeName = other.storeName;
    deliveryFee = other.deliveryFee;
    productVersion = other.productVersion;
    updatedDate = other.updatedDate;
    returnPickupBy = other.returnPickupBy;
    deliverable = other.deliverable;
    brandName = other.brandName;
    mrpPrice = other.mrpPrice;
    sellingPrice = other.sellingPrice;
    ordersCount = other.ordersCount;
    returnCount = other.returnCount;
    location = other.location;
    countOfRatings = other.countOfRatings;
    rating = other.rating;
    prodImages = other.prodImages;
    commentCount = other.commentCount;
    storeid = other.storeid;
    inStock = other.inStock;
    hashTag = other.hashTag;
    productCategory = other.productCategory;
    storeReference = other.storeReference;
    returnPeriod = other.returnPeriod;
    deliveryBy = other.deliveryBy;
    returnConditions = other.returnConditions;
    returnCostOn = other.returnCostOn;
    deliveryPartner = other.deliveryPartner;
    logisticPartnerName = other.logisticPartnerName;
    swadeshiOwned = other.swadeshiOwned;
    swadeshiMade = other.swadeshiMade;
    swadeshiBrand = other.swadeshiBrand;
    targetGender = other.targetGender;
    isDeleted = other.isDeleted;
    configReceiveOrders = other.configReceiveOrders;
    isTestStore = other.isTestStore;
    likeCount = other.likeCount;
    likeStatus = other.likeStatus;
    saveStatus = other.saveStatus;
    isBuyEnable = other.isBuyEnable;
    productStatusMessage = other.productStatusMessage;
    fulfillmentOptions = other.fulfillmentOptions;
    isPromotionEnabled = other.isPromotionEnabled;
    promotionAmount = other.promotionAmount;
    disclaimerMessage = other.disclaimerMessage;
    analyticsViewCount = other.analyticsViewCount;
    level = other.level;
    productSlug = other.productSlug;
    productCode = other.productCode;
    totalTaggedPosts = other.totalTaggedPosts;
    taggedPostImages = other.taggedPostImages;
  }
}

class ProdImages {
  int? productimageid;
  int? productid;
  String? productImage;
  String? createdBy;
  String? modifiedBy;
  int? reorder;

  ProdImages(
      {this.productimageid,
      this.productid,
      this.productImage,
      this.createdBy,
      this.modifiedBy,
      this.reorder});

  ProdImages.fromJson(Map<String, dynamic> json) {
    //If mediaID is not null and has data
    if (json['mediaId'] != null) {
      productImage = json['mediaPath'];
      reorder = json['order'];
      productimageid = int.tryParse(json['mediaId'].toString());
    } else {
      productimageid = json['productimageid'];
      productid = json['productid'];
      productImage = json['product_image'] ?? json['mediaPath'] ?? "";
      createdBy = json['created_by'];
      modifiedBy = json['modified_by'];
      reorder = json['reorder'] ?? json['order'];

      // Handle is_deleted field if present
      if (json['is_deleted'] != null) {
        // No need to store is_deleted, but we can use it for validation if needed
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productimageid'] = productimageid;
    data['productid'] = productid;
    data['product_image'] = productImage;
    data['created_by'] = createdBy;
    data['modified_by'] = modifiedBy;
    data['reorder'] = reorder;
    return data;
  }
}

class RefundResponsibility {
  String itemHeading;
  String itemText;
  String itemSubtext;

  RefundResponsibility({
    required this.itemHeading,
    required this.itemText,
    this.itemSubtext = '', // Optional field, default is an empty string
  });

  // Factory method to create from JSON
  factory RefundResponsibility.fromJson(Map<String, dynamic> json) {
    return RefundResponsibility(
      itemHeading: json['item_heading'] ?? json['itemHeading'],
      itemText: json['item_text'] ?? json['itemText'],
      itemSubtext: json['item_subtext'] ?? json['itemSubtext'],
    );
  }

  // Method to convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'item_heading': itemHeading,
      'item_text': itemText,
      'item_subtext': itemSubtext,
    };
  }
}

class TaggedStory {
  String? title;
  String? icon;
  String? storyReference;

  TaggedStory({this.title, this.icon, this.storyReference});

  TaggedStory.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    icon = json['icon'];
    storyReference = json['story_reference'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['title'] = title;
    data['icon'] = icon;
    data['story_reference'] = storyReference;
    return data;
  }
}
