import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/common_methods.dart';

class StoreInfo {
  int? storeid;
  String? storeReference;
  String? storeName;
  String? storeDesc;
  bool? isActive;
  String? businessDescription;
  int? createdBy;
  int? modifiedBy;
  String? icon;
  String? coverImage;
  String? storehandle;
  String? categoryName;
  String? gstNumber;
  String? swadeshiOwned;
  bool isLike = false;
  bool? isGstVerified;
  String? gstBusinessName;
  String? panNumber;
  String? panName;
  bool? isPanVerified;
  bool? deliverability;
  int? supports;
  int? sales;
  List<StoreDetails>? storeDetails;
  List<Storelinks>? storelinks;
  bool isSupported = false;
  bool? isDeleted;
  bool? configReceiveOrders;
  String? followStatus;
  String? storeSignature;
  bool? isTestStore;
  bool? visited = false;
  String? verificationType;
  bool? isVerificationCompleted;
  bool? hasProducts;
  bool? hasPosts;
  bool? isAutoWithdrawEnable;
  String? inviteCode;
  String? subscriptionType;
  int? unreadConversationsCount;
  String? messagingToken;
  String? firstVerifiedDate;
  String? newMessagingToken;
  String? newMessagingUserId;
  String? storeAiReference;
  String? storeAiMessagingToken;
  String? storeAiMessagingUserId;
  String? storeLevel;
  double? storeValuation;
  int? analyticsViewCount;
  int? storeReviewCount;
  double? storeAvgRating;
  int? storeProductReviewCount;
  double? storeProductAvgRating;
  String? storeUpdatesGroupId;
  String? previewStoreReference;

  StoreInfo({
    this.storeid,
    this.storeReference,
    this.storeName,
    this.storeDesc,
    this.storeSignature,
    this.messagingToken,
    this.isActive,
    this.businessDescription,
    this.createdBy,
    this.modifiedBy,
    this.gstBusinessName,
    this.unreadConversationsCount,
    this.icon,
    this.coverImage,
    this.storehandle,
    this.isTestStore,
    this.categoryName,
    this.isDeleted,
    this.gstNumber,
    this.isGstVerified,
    this.panNumber,
    this.panName,
    this.isPanVerified,
    this.deliverability,
    this.subscriptionType,
    this.supports,
    this.sales,
    this.storeDetails,
    this.followStatus,
    this.configReceiveOrders,
    this.swadeshiOwned,
    this.verificationType,
    this.isVerificationCompleted,
    this.storelinks,
    this.hasProducts,
    this.hasPosts,
    this.isAutoWithdrawEnable,
    this.inviteCode,
    this.firstVerifiedDate,
    this.newMessagingToken,
    this.newMessagingUserId,
    this.storeAiReference,
    this.storeAiMessagingToken,
    this.storeAiMessagingUserId,
    this.storeLevel,
    this.storeValuation,
    this.analyticsViewCount,
    this.storeReviewCount,
    this.storeAvgRating,
    this.storeProductReviewCount,
    this.storeProductAvgRating,
    this.storeUpdatesGroupId,
  });

  StoreInfo.fromJson(Map<String, dynamic> json) {
    storeid = json['storeid'];
    storeReference = (json['preview_store_reference'] == null || json['preview_store_reference'] == '')
        ? json['store_reference']
        : json['preview_store_reference'];
    storeName = json['store_name'];
    storeSignature = json['store_signature'];
    gstBusinessName = json['gst_business_name'];
    storeDesc = json['store_desc'];
    isTestStore = json['is_test_store'];
    // messagingToken = json['js_messaging_token']??AppConstants.appData.messagingToken;
    newMessagingToken = json['new_messaging_token'];
    newMessagingUserId = json['new_messaging_user_id'];
    storeAiReference = json['store_ai_reference'];
    storeAiMessagingToken = json['store_ai_messaging_token'];
    storeAiMessagingUserId = json['store_ai_messaging_user_id'];
    storeUpdatesGroupId = json['store_updates_group_id'];
    //If app constant store reference is same with userReference then only save reference in App constant else ignore .
    if (AppConstants.appData.isStoreView ?? false) {
      if (AppConstants.appData.storeReference == storeReference &&
          !CommonMethods().isStaticUser()) {
        // AppConstants.appData.messagingToken = messagingToken!;
        // Only update values if they are not null
        if (newMessagingToken != null) {
          AppConstants.appData.newMessagingToken = newMessagingToken;
        }
        if (newMessagingUserId != null) {
          AppConstants.appData.newMessagingUserId = newMessagingUserId;
        }
        if (storeAiReference != null) {
          AppConstants.appData.storeAiReference = storeAiReference;
        }
        if (storeAiMessagingToken != null) {
          AppConstants.appData.storeAiMessagingToken = storeAiMessagingToken;
        }
        if (storeAiMessagingUserId != null) {
          AppConstants.appData.storeAiMessagingUserId = storeAiMessagingUserId;
        }
      }
    }

    isActive = json['is_active'];
    followStatus = json['follow_status'] ?? "Support";
    businessDescription = json['business_description'];
    createdBy = json['created_by'];
    unreadConversationsCount = json['unread_conversations_count'];
    modifiedBy = json['modified_by'];
    icon = json['icon'];
    coverImage = json['cover_image'];
    configReceiveOrders = json['config_receive_orders'];
    storehandle = json['storehandle'] ?? "";
    isDeleted = json['deleted'] ?? false;
    categoryName = json['category_name'];
    gstNumber = json['gst_number'];
    isGstVerified = json['is_gst_verified'];
    panNumber = json['pan_number'];
    subscriptionType = json['subscription_type'];
    panName = json['pan_name'];
    isVerificationCompleted = json['is_verification_completed'];
    swadeshiOwned = json['swadeshi_owned'];
    isPanVerified = json['is_pan_verified'];
    verificationType = json['verification_type'] ?? "";
    deliverability = json['deliverability'];
    supports = json['supports'];
    hasProducts = json['has_products'] ?? true;
    inviteCode = json['invite_code'] ?? "SWSRADHAQ";
    hasPosts = json['has_posts'] ?? false;
    sales = json['sales'];
    isAutoWithdrawEnable = json['is_auto_withdrawal_enabled'] ?? false;
    firstVerifiedDate = json['first_verified_date'] ?? "";
    if (json['store_details'] != null) {
      storeDetails = <StoreDetails>[];
      json['store_details'].forEach((v) {
        storeDetails!.add(StoreDetails.fromJson(v));
      });
    }
    if (json['storelinks'] != null) {
      storelinks = <Storelinks>[];
      json['storelinks'].forEach((v) {
        storelinks!.add(Storelinks.fromJson(v));
      });
    }
    storeLevel = json['store_level'] ?? "1";
    storeValuation = json['store_valuation'] ?? 0.0;
    analyticsViewCount = json['analytics_view_count'] ?? 0;
    storeReviewCount = json['store_review_count'] ?? 0;
    storeAvgRating = json['store_avg_rating'] ?? 0.0;
    storeProductReviewCount = json['store_product_review_count'] ?? 0;
    storeProductAvgRating = json['store_product_avg_rating'] ?? 0.0;
    previewStoreReference = json['preview_store_reference'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['storeid'] = storeid;
    data['store_reference'] = storeReference;
    data['store_name'] = storeName;
    data['store_desc'] = storeDesc;
    data['store_signature'] = storeSignature;
    data['gst_business_name'] = gstBusinessName;
    data['is_active'] = isActive;
    data['is_verification_completed'] = isVerificationCompleted;
    data['follow_status'] = followStatus;
    data['is_test_store'] = isTestStore;
    data['business_description'] = businessDescription;
    data['created_by'] = createdBy;
    data['modified_by'] = modifiedBy;
    data['icon'] = icon;
    data['cover_image'] = coverImage;
    data['config_receive_orders'] = configReceiveOrders;
    data['storehandle'] = storehandle;
    data['subscription_type'] = subscriptionType;
    data['category_name'] = categoryName;
    data['gst_number'] = gstNumber;
    data['deleted'] = isDeleted;
    data['is_gst_verified'] = isGstVerified;
    data['pan_number'] = panNumber;
    data['pan_name'] = panName;
    data['unread_conversations_count'] = unreadConversationsCount;
    data['is_pan_verified'] = isPanVerified;
    data['swadeshi_owned'] = swadeshiOwned;
    data['deliverability'] = deliverability;
    data['supports'] = supports;
    data['verification_type'] = verificationType;
    data['sales'] = sales;
    data['has_products'] = hasProducts;
    data['has_posts'] = hasPosts;
    data['invite_code'] = inviteCode;
    data['is_auto_withdrawal_enabled'] = isAutoWithdrawEnable;
    data['first_verified_date'] = firstVerifiedDate;
    if (storeDetails != null) {
      data['store_details'] = storeDetails!.map((v) => v.toJson()).toList();
    }
    if (storelinks != null) {
      data['storelinks'] = storelinks!.map((v) => v.toJson()).toList();
    }
    data['store_level'] = storeLevel;
    data['store_valuation'] = storeValuation;
    data['analytics_view_count'] = analyticsViewCount;
    data['store_review_count'] = storeReviewCount;
    data['store_avg_rating'] = storeAvgRating;
    data['store_product_review_count'] = storeProductReviewCount;
    data['store_product_avg_rating'] = storeProductAvgRating;
    data['preview_store_reference'] = previewStoreReference;
    return data;
  }
}

class StoreDetails {
  String? location;
  String? state;
  List<String>? phoneNumber;
  List<String>? email;

  StoreDetails({this.location, this.state, this.phoneNumber, this.email});

  StoreDetails.fromJson(Map<String, dynamic> json) {
    location = json['location'];
    state = json['state'];
    // phoneNumber = json['phone_number'].cast<String>();
    // Check if 'phoneNumber' exists and is not null before casting.
    final phoneNumbers = json['phone_number'];
    if (phoneNumbers is List) {
      phoneNumber = phoneNumbers.cast<String>();
    } else {
      // If 'email' is null or not a list, assign an empty list.
      phoneNumber = [];
    }
    // email = json['email'].cast<String>();
    // Check if 'email' exists and is not null before casting.
    final emails = json['email'];
    if (emails is List) {
      email = emails.cast<String>();
    } else {
      // If 'email' is null or not a list, assign an empty list.
      email = [];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['location'] = location;
    data['state'] = state;
    data['phone_number'] = phoneNumber;
    data['email'] = email;
    return data;
  }
}

class Storelinks {
  int? storelinkid;
  int? storeid;
  String? storelinkName;
  String? storeLink;
  String? storeReference;
  String linkIcon = "";
  // String linkIcon = "";

  Storelinks(
      {this.storelinkid,
      this.storeid,
      this.storelinkName,
      this.storeLink,
      this.storeReference});

  Storelinks.fromJson(Map<String, dynamic> json) {
    storelinkid = json['storelinkid'];
    storeid = json['storeid'];
    storelinkName = json['storelink_name'];
    storeLink = json['store_link'];
    storeReference = json['store_reference'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['storelinkid'] = storelinkid;
    data['storeid'] = storeid;
    data['storelink_name'] = storelinkName;
    data['store_link'] = storeLink;
    data['store_reference'] = storeReference;
    return data;
  }
}
