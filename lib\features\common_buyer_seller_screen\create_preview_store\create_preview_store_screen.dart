import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/create_preview_store/create_preview_store_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class CreatePreviewStoreScreen extends StatefulWidget {
  const CreatePreviewStoreScreen({Key? key}) : super(key: key);

  @override
  _CreatePreviewStoreScreenState createState() => _CreatePreviewStoreScreenState();
}

class _CreatePreviewStoreScreenState extends State<CreatePreviewStoreScreen> {
  late CreatePreviewStoreBloc createPreviewStoreBloc;

  @override
  void initState() {
    createPreviewStoreBloc = CreatePreviewStoreBloc(context);
    createPreviewStoreBloc.init();
    super.initState();
  }

  @override
  void dispose() {
    createPreviewStoreBloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: AppColors.appWhite,
        appBar: AppBar(
          backgroundColor: AppColors.appWhite,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: AppColors.appBlack),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text(
            "Create Preview Store",
            style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
          ),
          centerTitle: true,
        ),
        body: SafeArea(child: body()),
        floatingActionButton: createButton(),
      ),
    );
  }

  //region Body
  Widget body() {
    return StreamBuilder<PreviewStoreCreationState>(
      stream: createPreviewStoreBloc.stateCtrl.stream,
      builder: (context, snapshot) {
        if (snapshot.data == PreviewStoreCreationState.Loading) {
          return Center(
            child: AppCommonWidgets.appCircularProgress(),
          );
        }
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              Text(
                "Preview stores are visible to you only. Perfect for testing and previewing your store setup.",
                style: AppTextStyle.contentText0(textColor: AppColors.writingColor2),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              
              // Store Icon Section
              Center(
                child: Column(
                  children: [
                    addLogo(),
                    const SizedBox(height: 10),
                    StreamBuilder<bool>(
                      stream: createPreviewStoreBloc.onTextChangeCtrl.stream,
                      builder: (context, snapshot) {
                        return Text(
                          createPreviewStoreBloc.previewStoreNameCtrl.text.isEmpty
                              ? "Preview Store Name"
                              : createPreviewStoreBloc.previewStoreNameCtrl.text.trim(),
                          textAlign: TextAlign.center,
                          style: AppTextStyle.usernameHeading(textColor: AppColors.appBlack),
                        );
                      }
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 40),
              
              // Form Fields
              storeNameField(),
              const SizedBox(height: 30),
              storeHandleField(),
              
              AppCommonWidgets.bottomListSpace(context: context),
            ],
          ),
        );
      }
    );
  }
  //endregion

  //region Add Logo
  Widget addLogo() {
    return StreamBuilder<bool>(
      stream: createPreviewStoreBloc.logoCtrl.stream,
      builder: (context, snapshot) {
        return InkWell(
          onTap: () {
            createPreviewStoreBloc.goToAddImageScreen();
          },
          child: SizedBox(
            height: 106,
            width: 106,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(
                CommonMethods().getBorderRadius(
                  height: 106,
                  imageType: CustomImageContainerType.store,
                ),
              ),
              child: createPreviewStoreBloc.isImageSelected
                  ? kIsWeb && createPreviewStoreBloc.webImageBytes != null
                      // Web platform with image selected
                      ? Image.memory(
                          createPreviewStoreBloc.webImageBytes!,
                          fit: BoxFit.cover,
                          cacheHeight: 300,
                          cacheWidth: 300,
                        )
                      // Mobile platform with image selected
                      : Image.file(
                          File(createPreviewStoreBloc.files.path),
                          fit: BoxFit.cover,
                          cacheHeight: 300,
                          cacheWidth: 300,
                        )
                  // No image selected
                  : const CustomImageContainer(
                      width: 106,
                      height: 106,
                      imageUrl: AppImages.storePlaceHolder,
                      imageType: CustomImageContainerType.store,
                    ),
            ),
          ),
        );
      }
    );
  }
  //endregion

  //region Store Name Field
  Widget storeNameField() {
    return AppTitleAndOptions(
      title: "Preview Store Name",
      option: AppTextFields.allTextField(
        maxEntry: 40,
        onChanged: () {
          createPreviewStoreBloc.onTextChange();
        },
        context: context,
        textEditingController: createPreviewStoreBloc.previewStoreNameCtrl,
        hintText: "Enter your preview store name",
      ),
    );
  }
  //endregion

  //region Store Handle Field
  Widget storeHandleField() {
    return AppTitleAndOptions(
      title: "Preview Store Handle",
      option: AppTextFields.userNameTextField(
        maxEntry: 30,
        onChanged: () {
          createPreviewStoreBloc.onTextChange();
        },
        context: context,
        textEditingController: createPreviewStoreBloc.previewStoreHandleCtrl,
        hintText: "Enter your preview store handle",
      ),
    );
  }
  //endregion

  //region Create Button
  Widget createButton() {
    return StreamBuilder<PreviewStoreCreationState>(
      stream: createPreviewStoreBloc.stateCtrl.stream,
      builder: (context, snapshot) {
        bool isLoading = snapshot.data == PreviewStoreCreationState.Loading;
        
        return FloatingActionButton.extended(
          backgroundColor: AppColors.brandBlack,
          onPressed: isLoading ? null : () {
            createPreviewStoreBloc.createPreviewStore();
          },
          label: isLoading
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.appWhite),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      "Creating...",
                      style: AppTextStyle.access0(textColor: AppColors.appWhite),
                    ),
                  ],
                )
              : Text(
                  "Create Preview Store",
                  style: AppTextStyle.access0(textColor: AppColors.appWhite),
                ),
        );
      }
    );
  }
  //endregion
}
