import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/congratulations_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class CongratulationsScreen extends StatefulWidget {
  final String userReference;
  final Map<String, dynamic> userData;
  final String? profilePicturePath;

  const CongratulationsScreen({
    Key? key,
    required this.userReference,
    required this.userData,
    this.profilePicturePath,
  }) : super(key: key);

  @override
  _CongratulationsScreenState createState() => _CongratulationsScreenState();
}

class _CongratulationsScreenState extends State<CongratulationsScreen> {
  //region Bloc
  late CongratulationsBloc congratulationsBloc;

  //endregion

  //region Init
  @override
  void initState() {
    congratulationsBloc = CongratulationsBloc(context, widget.userReference,
        widget.userData, widget.profilePicturePath);
    congratulationsBloc.init();
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: SafeArea(
        child: body(),
      ),
    );
  }

  //region Body
  Widget body() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 40),
            header(),
            const SizedBox(height: 30),
            profileSection(),
            const SizedBox(height: 30),
            contributionSection(),
            const SizedBox(height: 30),
            supportScoreSection(),
            const SizedBox(height: 30),
            storeOwnerSection(),
            const SizedBox(height: 40),
            continueButton(),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Header
  Widget header() {
    return Column(
      children: [
        Text(
          "Congratulations!\nYou are now a member of Swadeshi Community",
          style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
  //endregion

  //region Profile Section
  Widget profileSection() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: AppColors.borderColor1),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(50),
            child: widget.profilePicturePath == null
                ? SvgPicture.asset(
                    AppImages.userPlaceHolder,
                    fit: BoxFit.cover,
                  )
                : Image.file(
                    File(widget.profilePicturePath!),
                    fit: BoxFit.cover,
                  ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          widget.userData["user_name"],
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
      ],
    );
  }
  //endregion

  //region Contribution Section
  Widget contributionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Your contribution pays!",
          style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 16),
        Text(
          "Every time you support a store, leave a review, share feedback, or help someone discover a product — you're growing the Swadeshi community.",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
      ],
    );
  }
  //endregion

  //region Support Score Section
  Widget supportScoreSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "We track this through your Support Score — a number that reflects how much you've contributed. As your score increases, you level up from Level 0 to Level 9.",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 16),
        Text(
          "Each level brings you new benefits: higher discounts to your purchases, discounted group-buying access, extra commissions on purchases through your recommendations, special recognition and influence within the community.",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 16),
        Text(
          ">> This isn't just about points — it's about making an impact, and being seen for it.",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 16),
        GestureDetector(
          onTap: () {
            CommonMethods.openAppWebView(
              webUrl: AppConstants.supportScoreMapUrl,
              context: context,
            );
          },
          child: Text(
            "View Support Score Map & Levels",
            style: AppTextStyle.contentHeading0(
                textColor: AppColors.brandBlack, isUnderline: true),
          ),
        ),
      ],
    );
  }
  //endregion

  //region Store Owner Section
  Widget storeOwnerSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "For Store Owners:",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 8),
        Text(
          "Your store has a Support Score too — tied to real-time valuation and growth.",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 16),
        GestureDetector(
          onTap: () {
            CommonMethods.openAppWebView(
              webUrl: AppConstants.storeLevelUpUrl,
              context: context,
            );
          },
          child: Text(
            "How Stores Level Up?",
            style: AppTextStyle.contentHeading0(
              textColor: AppColors.brandBlack,
              isUnderline: true,
            ),
          ),
        ),
      ],
    );
  }
  //endregion

  //region Continue Button
  Widget continueButton() {
    return StreamBuilder<bool>(
      stream: congratulationsBloc.loadingCtrl.stream,
      initialData: false,
      builder: (context, snapshot) {
        bool isLoading = snapshot.data ?? false;

        return SizedBox(
          width: double.infinity,
          child: CupertinoButton(
            borderRadius: BorderRadius.circular(11),
            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 15),
            color: AppColors.appBlack,
            onPressed:
                isLoading ? null : () => congratulationsBloc.continueToHome(),
            child: isLoading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: AppColors.appWhite,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    "Continue to Swadesi Community →",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: AppTextStyle.access0(textColor: AppColors.appWhite),
                  ),
          ),
        );
      },
    );
  }
  //endregion
}
